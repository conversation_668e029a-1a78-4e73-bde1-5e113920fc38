<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> Cursor 雪碧图生成器</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .generator-section {
            margin-bottom: 40px;
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 15px;
        }
        
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .control-group {
            display: flex;
            flex-direction: column;
        }
        
        label {
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        select, input, button {
            padding: 10px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
        }
        
        select, input {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
        }
        
        button {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            cursor: pointer;
            font-weight: bold;
            transition: transform 0.2s;
        }
        
        button:hover {
            transform: translateY(-2px);
        }
        
        .preview-area {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .preview-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
        }
        
        .sprite-preview {
            width: 192px;
            height: 128px;
            margin: 0 auto 15px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.05);
            display: flex;
            flex-wrap: wrap;
        }
        
        .frame {
            width: 32px;
            height: 32px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            position: relative;
            overflow: hidden;
        }
        
        .download-btn {
            width: 100%;
            margin-top: 10px;
        }
        
        .instructions {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 15px;
            margin-top: 30px;
        }
        
        .instructions h3 {
            margin-top: 0;
        }
        
        .instructions ol {
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐾 Pet Cursor 雪碧图生成器</h1>
        
        <div class="generator-section">
            <h2>生成设置</h2>
            <div class="controls">
                <div class="control-group">
                    <label for="petType">动物类型:</label>
                    <select id="petType">
                        <option value="cat">🐱 小猫咪</option>
                        <option value="dog">🐶 小狗狗</option>
                        <option value="rabbit">🐰 小兔子</option>
                        <option value="bird">🐦 小鸟</option>
                        <option value="panda">🐼 小熊猫</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label for="frameSize">帧尺寸:</label>
                    <select id="frameSize">
                        <option value="32">32x32px (默认)</option>
                        <option value="24">24x24px (小)</option>
                        <option value="48">48x48px (大)</option>
                        <option value="64">64x64px (超大)</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label for="style">绘画风格:</label>
                    <select id="style">
                        <option value="cute">可爱卡通</option>
                        <option value="pixel">像素艺术</option>
                        <option value="minimal">简约风格</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <button onclick="generateSprite()">生成雪碧图</button>
                </div>
            </div>
        </div>
        
        <div class="preview-area" id="previewArea">
            <!-- 预览卡片将在这里生成 -->
        </div>
        
        <div class="instructions">
            <h3>📋 使用说明</h3>
            <ol>
                <li>选择要生成的动物类型和设置</li>
                <li>点击"生成雪碧图"按钮</li>
                <li>预览生成的雪碧图效果</li>
                <li>点击"下载PNG"按钮保存文件</li>
                <li>将下载的文件放入 <code>assets/sprites/</code> 目录</li>
                <li>重新加载浏览器插件即可看到效果</li>
            </ol>
            
            <h3>🎨 雪碧图布局</h3>
            <p>每个雪碧图包含4行动画帧：</p>
            <ul>
                <li><strong>第0行</strong>: 静止状态 (4帧呼吸动画)</li>
                <li><strong>第1行</strong>: 移动状态 (6帧跑步动画)</li>
                <li><strong>第2行</strong>: 点击状态 (4帧互动动画)</li>
                <li><strong>第3行</strong>: 悬停状态 (4帧好奇动画)</li>
            </ul>
        </div>
    </div>

    <script>
        const petConfigs = {
            cat: {
                name: '小猫咪',
                emoji: '🐱',
                colors: ['#FF6B6B', '#FFE5E5'],
                features: ['耳朵', '胡须', '尾巴']
            },
            dog: {
                name: '小狗狗', 
                emoji: '🐶',
                colors: ['#4ECDC4', '#E8F8F7'],
                features: ['耳朵', '舌头', '尾巴']
            },
            rabbit: {
                name: '小兔子',
                emoji: '🐰', 
                colors: ['#45B7D1', '#E3F2FD'],
                features: ['长耳朵', '短尾巴', '胡萝卜']
            },
            bird: {
                name: '小鸟',
                emoji: '🐦',
                colors: ['#96CEB4', '#F0F8F4'], 
                features: ['翅膀', '喙', '羽毛']
            },
            panda: {
                name: '小熊猫',
                emoji: '🐼',
                colors: ['#FECA57', '#FFF8E1'],
                features: ['黑眼圈', '竹子', '圆耳朵']
            }
        };

        function generateSprite() {
            const petType = document.getElementById('petType').value;
            const frameSize = parseInt(document.getElementById('frameSize').value);
            const style = document.getElementById('style').value;
            
            const config = petConfigs[petType];
            
            // 清空预览区域
            const previewArea = document.getElementById('previewArea');
            previewArea.innerHTML = '';
            
            // 创建预览卡片
            const card = createPreviewCard(petType, config, frameSize, style);
            previewArea.appendChild(card);
        }

        function createPreviewCard(petType, config, frameSize, style) {
            const card = document.createElement('div');
            card.className = 'preview-card';
            
            card.innerHTML = `
                <h3>${config.emoji} ${config.name}</h3>
                <div class="sprite-preview" id="sprite-${petType}">
                    ${createFrames(petType, config, frameSize, style)}
                </div>
                <p>尺寸: ${frameSize}x${frameSize}px | 风格: ${style}</p>
                <button class="download-btn" onclick="downloadSprite('${petType}', ${frameSize})">
                    下载 ${petType}.png
                </button>
            `;
            
            return card;
        }

        function createFrames(petType, config, frameSize, style) {
            let frames = '';
            const animations = ['idle', 'walk', 'click', 'hover'];
            const frameCounts = [4, 6, 4, 4];
            
            for (let row = 0; row < 4; row++) {
                for (let col = 0; col < frameCounts[row]; col++) {
                    frames += `<div class="frame" style="background: linear-gradient(45deg, ${config.colors[0]}, ${config.colors[1]}); position: relative;">
                        ${config.emoji}
                        <div style="position: absolute; bottom: 2px; right: 2px; font-size: 8px; opacity: 0.7;">
                            ${row},${col}
                        </div>
                    </div>`;
                }
                // 填充空白帧到6列
                for (let col = frameCounts[row]; col < 6; col++) {
                    frames += `<div class="frame" style="background: rgba(255,255,255,0.1);"></div>`;
                }
            }
            
            return frames;
        }

        function downloadSprite(petType, frameSize) {
            // 创建canvas
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            // 设置canvas尺寸 (6列 x 4行)
            canvas.width = frameSize * 6;
            canvas.height = frameSize * 4;
            
            const config = petConfigs[petType];
            
            // 绘制雪碧图
            drawSpriteSheet(ctx, petType, config, frameSize);
            
            // 下载
            const link = document.createElement('a');
            link.download = `${petType}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }

        function drawSpriteSheet(ctx, petType, config, frameSize) {
            const frameCounts = [4, 6, 4, 4];
            
            for (let row = 0; row < 4; row++) {
                for (let col = 0; col < frameCounts[row]; col++) {
                    const x = col * frameSize;
                    const y = row * frameSize;
                    
                    // 绘制背景
                    const gradient = ctx.createLinearGradient(x, y, x + frameSize, y + frameSize);
                    gradient.addColorStop(0, config.colors[0]);
                    gradient.addColorStop(1, config.colors[1]);
                    
                    ctx.fillStyle = gradient;
                    ctx.fillRect(x, y, frameSize, frameSize);
                    
                    // 绘制简单的动物形状
                    drawAnimalShape(ctx, petType, x, y, frameSize, row, col);
                }
            }
        }

        function drawAnimalShape(ctx, petType, x, y, size, row, col) {
            const centerX = x + size / 2;
            const centerY = y + size / 2;
            const radius = size * 0.3;
            
            // 绘制主体
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
            ctx.fill();
            
            // 根据动物类型绘制特征
            switch (petType) {
                case 'cat':
                    drawCatFeatures(ctx, centerX, centerY, size);
                    break;
                case 'dog':
                    drawDogFeatures(ctx, centerX, centerY, size);
                    break;
                case 'rabbit':
                    drawRabbitFeatures(ctx, centerX, centerY, size);
                    break;
                case 'bird':
                    drawBirdFeatures(ctx, centerX, centerY, size);
                    break;
                case 'panda':
                    drawPandaFeatures(ctx, centerX, centerY, size);
                    break;
            }
        }

        function drawCatFeatures(ctx, centerX, centerY, size) {
            // 耳朵
            ctx.fillStyle = 'rgba(255, 107, 107, 0.8)';
            ctx.beginPath();
            ctx.moveTo(centerX - size * 0.2, centerY - size * 0.25);
            ctx.lineTo(centerX - size * 0.1, centerY - size * 0.4);
            ctx.lineTo(centerX - size * 0.05, centerY - size * 0.25);
            ctx.fill();
            
            ctx.beginPath();
            ctx.moveTo(centerX + size * 0.2, centerY - size * 0.25);
            ctx.lineTo(centerX + size * 0.1, centerY - size * 0.4);
            ctx.lineTo(centerX + size * 0.05, centerY - size * 0.25);
            ctx.fill();
        }

        function drawDogFeatures(ctx, centerX, centerY, size) {
            // 耳朵
            ctx.fillStyle = 'rgba(78, 205, 196, 0.8)';
            ctx.beginPath();
            ctx.ellipse(centerX - size * 0.15, centerY - size * 0.2, size * 0.08, size * 0.15, -0.3, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.beginPath();
            ctx.ellipse(centerX + size * 0.15, centerY - size * 0.2, size * 0.08, size * 0.15, 0.3, 0, Math.PI * 2);
            ctx.fill();
        }

        function drawRabbitFeatures(ctx, centerX, centerY, size) {
            // 长耳朵
            ctx.fillStyle = 'rgba(69, 183, 209, 0.8)';
            ctx.beginPath();
            ctx.ellipse(centerX - size * 0.1, centerY - size * 0.3, size * 0.05, size * 0.2, -0.2, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.beginPath();
            ctx.ellipse(centerX + size * 0.1, centerY - size * 0.3, size * 0.05, size * 0.2, 0.2, 0, Math.PI * 2);
            ctx.fill();
        }

        function drawBirdFeatures(ctx, centerX, centerY, size) {
            // 翅膀
            ctx.fillStyle = 'rgba(150, 206, 180, 0.8)';
            ctx.beginPath();
            ctx.ellipse(centerX - size * 0.25, centerY, size * 0.15, size * 0.08, -0.3, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.beginPath();
            ctx.ellipse(centerX + size * 0.25, centerY, size * 0.15, size * 0.08, 0.3, 0, Math.PI * 2);
            ctx.fill();
        }

        function drawPandaFeatures(ctx, centerX, centerY, size) {
            // 黑眼圈
            ctx.fillStyle = 'rgba(0, 0, 0, 0.6)';
            ctx.beginPath();
            ctx.arc(centerX - size * 0.1, centerY - size * 0.05, size * 0.08, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.beginPath();
            ctx.arc(centerX + size * 0.1, centerY - size * 0.05, size * 0.08, 0, Math.PI * 2);
            ctx.fill();
            
            // 黑耳朵
            ctx.beginPath();
            ctx.arc(centerX - size * 0.15, centerY - size * 0.25, size * 0.08, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.beginPath();
            ctx.arc(centerX + size * 0.15, centerY - size * 0.25, size * 0.08, 0, Math.PI * 2);
            ctx.fill();
        }

        // 页面加载时生成默认预览
        window.onload = function() {
            generateSprite();
        };
    </script>
</body>
</html>
