#!/usr/bin/env node

/**
 * Pet Cursor 构建脚本
 * 用于打包发布版本
 */

const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

class BuildScript {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.buildDir = path.join(this.projectRoot, 'build');
    this.distDir = path.join(this.projectRoot, 'dist');
    
    this.includeFiles = [
      'manifest.json',
      'popup/',
      'content/',
      'background/',
      'utils/',
      'assets/',
      'README.md',
      'CHANGELOG.md'
    ];
    
    this.excludePatterns = [
      '.git',
      '.gitignore',
      'node_modules',
      'scripts',
      'test',
      'docs',
      '.vscode',
      '.DS_Store',
      'Thumbs.db',
      '*.log',
      '*.tmp'
    ];
  }

  async build() {
    console.log('🚀 开始构建 Pet Cursor...');
    
    try {
      // 清理构建目录
      await this.cleanBuildDir();
      
      // 创建构建目录
      await this.createBuildDir();
      
      // 复制文件
      await this.copyFiles();
      
      // 优化资源
      await this.optimizeAssets();
      
      // 验证构建
      await this.validateBuild();
      
      // 创建发布包
      await this.createReleasePackage();
      
      console.log('✅ 构建完成！');
      console.log(`📦 发布包位置: ${this.distDir}`);
      
    } catch (error) {
      console.error('❌ 构建失败:', error);
      process.exit(1);
    }
  }

  async cleanBuildDir() {
    console.log('🧹 清理构建目录...');
    
    if (fs.existsSync(this.buildDir)) {
      await this.removeDirectory(this.buildDir);
    }
    
    if (fs.existsSync(this.distDir)) {
      await this.removeDirectory(this.distDir);
    }
  }

  async createBuildDir() {
    console.log('📁 创建构建目录...');
    
    fs.mkdirSync(this.buildDir, { recursive: true });
    fs.mkdirSync(this.distDir, { recursive: true });
  }

  async copyFiles() {
    console.log('📋 复制文件...');
    
    for (const file of this.includeFiles) {
      const sourcePath = path.join(this.projectRoot, file);
      const targetPath = path.join(this.buildDir, file);
      
      if (fs.existsSync(sourcePath)) {
        const stat = fs.statSync(sourcePath);
        
        if (stat.isDirectory()) {
          await this.copyDirectory(sourcePath, targetPath);
        } else {
          await this.copyFile(sourcePath, targetPath);
        }
        
        console.log(`  ✓ ${file}`);
      } else {
        console.warn(`  ⚠️  文件不存在: ${file}`);
      }
    }
  }

  async copyDirectory(source, target) {
    fs.mkdirSync(target, { recursive: true });
    
    const files = fs.readdirSync(source);
    
    for (const file of files) {
      if (this.shouldExclude(file)) continue;
      
      const sourcePath = path.join(source, file);
      const targetPath = path.join(target, file);
      const stat = fs.statSync(sourcePath);
      
      if (stat.isDirectory()) {
        await this.copyDirectory(sourcePath, targetPath);
      } else {
        await this.copyFile(sourcePath, targetPath);
      }
    }
  }

  async copyFile(source, target) {
    const targetDir = path.dirname(target);
    fs.mkdirSync(targetDir, { recursive: true });
    fs.copyFileSync(source, target);
  }

  shouldExclude(filename) {
    return this.excludePatterns.some(pattern => {
      if (pattern.includes('*')) {
        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
        return regex.test(filename);
      }
      return filename === pattern || filename.startsWith(pattern);
    });
  }

  async optimizeAssets() {
    console.log('🎨 优化资源文件...');
    
    // 检查图片文件
    const assetsDir = path.join(this.buildDir, 'assets');
    if (fs.existsSync(assetsDir)) {
      await this.optimizeImages(assetsDir);
    }
    
    // 压缩CSS和JS（如果需要）
    await this.optimizeCode();
  }

  async optimizeImages(assetsDir) {
    const imageExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.svg'];
    
    const checkImages = (dir) => {
      const files = fs.readdirSync(dir);
      
      for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
          checkImages(filePath);
        } else {
          const ext = path.extname(file).toLowerCase();
          if (imageExtensions.includes(ext)) {
            const size = stat.size;
            console.log(`  📷 ${file}: ${(size / 1024).toFixed(2)}KB`);
            
            // 检查文件大小
            if (size > 100 * 1024) { // 100KB
              console.warn(`    ⚠️  文件较大，建议压缩`);
            }
          }
        }
      }
    };
    
    checkImages(assetsDir);
  }

  async optimizeCode() {
    // 这里可以添加代码压缩逻辑
    // 例如使用 terser 压缩 JavaScript
    // 或使用 cssnano 压缩 CSS
    console.log('  📝 代码优化（跳过，保持可读性）');
  }

  async validateBuild() {
    console.log('🔍 验证构建...');
    
    // 检查必要文件
    const requiredFiles = [
      'manifest.json',
      'popup/popup.html',
      'popup/popup.js',
      'popup/popup.css',
      'content/content.js',
      'content/content.css',
      'background/background.js',
      'utils/sprite-manager.js'
    ];
    
    for (const file of requiredFiles) {
      const filePath = path.join(this.buildDir, file);
      if (!fs.existsSync(filePath)) {
        throw new Error(`缺少必要文件: ${file}`);
      }
    }
    
    // 验证 manifest.json
    await this.validateManifest();
    
    console.log('  ✅ 构建验证通过');
  }

  async validateManifest() {
    const manifestPath = path.join(this.buildDir, 'manifest.json');
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
    
    // 检查必要字段
    const requiredFields = ['name', 'version', 'description', 'manifest_version'];
    for (const field of requiredFields) {
      if (!manifest[field]) {
        throw new Error(`manifest.json 缺少字段: ${field}`);
      }
    }
    
    // 检查版本格式
    const versionRegex = /^\d+\.\d+\.\d+$/;
    if (!versionRegex.test(manifest.version)) {
      throw new Error(`版本号格式错误: ${manifest.version}`);
    }
    
    console.log(`  📋 Manifest 验证通过 (v${manifest.version})`);
  }

  async createReleasePackage() {
    console.log('📦 创建发布包...');
    
    const manifest = JSON.parse(fs.readFileSync(path.join(this.buildDir, 'manifest.json'), 'utf8'));
    const version = manifest.version;
    const packageName = `pet-cursor-v${version}.zip`;
    const packagePath = path.join(this.distDir, packageName);
    
    await this.createZip(this.buildDir, packagePath);
    
    const stat = fs.statSync(packagePath);
    console.log(`  ✅ 发布包创建完成: ${packageName} (${(stat.size / 1024 / 1024).toFixed(2)}MB)`);
    
    // 创建开发版本（未压缩）
    const devPackageName = `pet-cursor-v${version}-dev.zip`;
    const devPackagePath = path.join(this.distDir, devPackageName);
    await this.createZip(this.buildDir, devPackagePath, false);
    
    console.log(`  📝 开发版本: ${devPackageName}`);
  }

  async createZip(sourceDir, targetPath, compress = true) {
    return new Promise((resolve, reject) => {
      const output = fs.createWriteStream(targetPath);
      const archive = archiver('zip', {
        zlib: { level: compress ? 9 : 0 }
      });
      
      output.on('close', () => resolve());
      archive.on('error', reject);
      
      archive.pipe(output);
      archive.directory(sourceDir, false);
      archive.finalize();
    });
  }

  async removeDirectory(dir) {
    if (fs.existsSync(dir)) {
      const files = fs.readdirSync(dir);
      
      for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
          await this.removeDirectory(filePath);
        } else {
          fs.unlinkSync(filePath);
        }
      }
      
      fs.rmdirSync(dir);
    }
  }
}

// 运行构建
if (require.main === module) {
  const builder = new BuildScript();
  builder.build();
}

module.exports = BuildScript;
