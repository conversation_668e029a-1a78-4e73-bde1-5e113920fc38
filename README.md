# 🐾 Pet Cursor - 可爱动物鼠标跟随

让你的浏览器充满生机！Pet Cursor 为你带来可爱的小动物，它们会跟随你的鼠标在网页上活泼地移动。

![Pet Cursor Demo](assets/demo.gif)

## ✨ 主要功能

### 🐱 5种可爱动物
- **小猫咪** - 活泼可爱的小猫，会跟随鼠标跑来跑去
- **小狗狗** - 忠诚的小狗，摇着尾巴跟在你身后
- **小兔子** - 蹦蹦跳跳的小兔子，充满活力
- **小鸟** - 自由飞翔的小鸟，轻盈灵动
- **小熊猫** - 憨态可掬的熊猫，萌化你的心

### 🎭 丰富动画状态
- **静止状态** - 缓慢的呼吸动画，生动自然
- **移动状态** - 跟随鼠标移动时的跑步/飞行动画
- **点击状态** - 鼠标点击时的特殊互动动画
- **悬停状态** - 鼠标悬停时的好奇张望动画

### ⚙️ 个性化设置
- **动物选择** - 5种动物随心切换
- **动画速度** - 0.5x 到 2x 速度调节
- **透明度** - 30% 到 100% 透明度控制
- **大小调节** - 24px 到 64px 尺寸选择
- **跟随灵敏度** - 调节跟随的响应速度

### 🛡️ 智能优化
- **性能监控** - 实时监控动画性能，自动优化
- **兼容性检查** - 自动检测并修复网站兼容性问题
- **性能模式** - 低性能设备自动启用优化模式
- **网站管理** - 为特定网站禁用或启用功能

## 🚀 安装使用

### 安装方法
1. 访问 [Chrome Web Store](https://chrome.google.com/webstore)
2. 搜索 "Pet Cursor"
3. 点击 "添加至 Chrome"
4. 确认安装

### 使用步骤
1. **打开设置** - 点击浏览器工具栏中的 Pet Cursor 图标
2. **选择动物** - 在设置面板中选择你喜欢的小动物
3. **调整设置** - 根据喜好调整动画速度、大小和透明度
4. **开始体验** - 移动鼠标，享受可爱的跟随效果！

## 📱 界面预览

### 设置面板
![Settings Panel](assets/screenshots/settings.png)

设置面板包含：
- 插件开关控制
- 动物类型选择
- 动画参数调节
- 高级设置选项
- 网站管理功能

### 动画效果
![Animation Effects](assets/screenshots/animations.png)

不同动画状态展示：
- 静止时的呼吸动画
- 移动时的跑步动画
- 点击时的互动动画
- 悬停时的好奇动画

## 🔧 高级功能

### 网站管理
- **当前网站控制** - 一键禁用/启用当前网站
- **禁用列表** - 管理所有禁用的网站
- **批量操作** - 快速添加或移除网站

### 性能优化
- **自动检测** - 检测设备性能并自动调整
- **性能模式** - 低性能设备的优化模式
- **内存管理** - 防止内存泄漏，确保稳定运行

### 兼容性处理
- **样式冲突** - 自动处理CSS样式冲突
- **z-index问题** - 智能调整显示层级
- **全屏适配** - 全屏模式下的特殊处理

## 🎯 适用场景

### 日常使用
- **网页浏览** - 让日常浏览更有趣味
- **工作学习** - 缓解长时间使用电脑的疲劳
- **休闲娱乐** - 为游戏和娱乐增添乐趣

### 专业场景
- **演示展示** - 为演示文稿增添可爱元素
- **直播录制** - 让直播内容更生动有趣
- **教学培训** - 吸引学生注意力

## 🔒 隐私安全

### 隐私保护
- ✅ **不收集个人信息** - 完全不收集任何用户数据
- ✅ **本地存储** - 所有设置仅保存在本地浏览器
- ✅ **不访问网页内容** - 不读取或修改网页内容
- ✅ **无网络请求** - 不向外部服务器发送数据

### 权限说明
- **存储权限** - 仅用于保存用户设置
- **活动标签页权限** - 在当前网页显示动画
- **脚本注入权限** - 实现鼠标跟随功能

## 🛠️ 技术规格

### 浏览器支持
- Chrome 88+
- Edge 88+
- Firefox 85+ (计划支持)

### 性能要求
- **CPU占用** < 5%
- **内存占用** < 50MB
- **动画帧率** 30-60fps

### 兼容性
- 支持主流网站
- 自动处理样式冲突
- 响应式设计适配

## 🐛 问题反馈

### 常见问题

**Q: 动画卡顿怎么办？**
A: 插件会自动检测性能并启用优化模式。你也可以手动启用性能模式或降低动画速度。

**Q: 在某些网站不显示怎么办？**
A: 检查该网站是否在禁用列表中，或者尝试刷新页面。某些特殊页面（如chrome://）不支持插件。

**Q: 如何完全关闭插件？**
A: 在设置面板中关闭插件开关，或在浏览器扩展管理页面中禁用插件。

**Q: 动物显示位置不对？**
A: 这可能是网站样式冲突导致的，插件会自动尝试修复。如果问题持续，请反馈给我们。

### 反馈渠道
- **GitHub Issues**: [提交问题](https://github.com/username/pet-cursor/issues)
- **邮箱支持**: <EMAIL>
- **Chrome商店评论**: 在商店页面留下评论

## 🎉 更新日志

### v1.0.0 (2024-01-01)
- 🎉 首次发布
- ✨ 5种可爱动物
- ⚙️ 完整设置界面
- 🚀 性能优化系统
- 🛡️ 兼容性检查

### 计划更新
- 🐸 新增更多动物类型
- 🎨 自定义动物上传
- 🔊 声音效果支持
- 🎄 节日主题模式

## 💝 支持项目

如果你喜欢 Pet Cursor，请：
- ⭐ 在 Chrome Web Store 给我们五星好评
- 🐛 报告问题帮助我们改进
- 💡 提出新功能建议
- 📢 推荐给朋友使用

## 📄 开源协议

本项目采用 MIT 协议开源，详见 [LICENSE](LICENSE) 文件。

## 🔧 开发指南

### 📁 项目结构
```
pet-cursor/
├── 📄 manifest.json          # 插件清单文件
├── 📁 assets/                 # 资源文件目录
│   ├── 🖼️ icons/             # 插件图标文件
│   └── 🎨 sprites/           # 动物雪碧图文件
├── 🔧 background/            # 后台服务脚本
├── 📝 content/               # 内容脚本目录
├── 🎛️ popup/                 # 设置面板目录
├── 🛠️ utils/                 # 工具类目录
├── 🔨 tools/                 # 开发工具目录
└── 🧪 test/                  # 测试文件目录
```

### 🛠️ 本地开发
```bash
# 1. 生成临时雪碧图
node tools/create-temp-sprites.js

# 2. 在Chrome中加载插件
# 进入 chrome://extensions/
# 启用开发者模式，加载项目根目录

# 3. 测试功能
# 打开 test/plugin-test.html 进行测试
```

### 📖 相关文档
- 📋 [安装指南](INSTALL_GUIDE.md) - 详细安装步骤
- 🔧 [技术文档](TECHNICAL_GUIDE.md) - 技术实现细节

## 👥 开发团队

Pet Cursor 由充满爱心的开发者团队打造，致力于为用户带来快乐的浏览体验。

---

**让可爱的小动物陪伴你的每一次浏览！** 🐾✨
