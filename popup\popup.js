// Pet Cursor Popup 控制脚本
class PopupController {
  constructor() {
    this.settings = {
      enabled: true,
      petType: 'cat',
      speed: 1.0,
      opacity: 100,
      size: 32,
      sensitivity: 0.1,
      clickAnimation: true,
      hoverAnimation: true,
      disabledSites: [],
      performanceMode: false
    };

    this.previewAnimator = null;
    
    this.init();
  }

  async init() {
    // 加载保存的设置
    await this.loadSettings();
    
    // 初始化UI
    this.initializeUI();
    
    // 绑定事件
    this.bindEvents();
    
    // 初始化预览
    this.initPreview();
  }

  async loadSettings() {
    try {
      const result = await chrome.storage.sync.get('petCursorSettings');
      if (result.petCursorSettings) {
        this.settings = { ...this.settings, ...result.petCursorSettings };
      }
    } catch (error) {
      console.log('加载设置失败，使用默认设置');
    }
  }

  async saveSettings() {
    try {
      await chrome.storage.sync.set({ petCursorSettings: this.settings });
      
      // 通知content script更新设置
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tab) {
        chrome.tabs.sendMessage(tab.id, {
          type: 'SETTINGS_UPDATED',
          settings: this.settings
        });
      }
      
      // 显示保存成功提示
      this.showSaveSuccess();
    } catch (error) {
      console.error('保存设置失败:', error);
    }
  }

  initializeUI() {
    // 设置开关状态
    document.getElementById('enableToggle').checked = this.settings.enabled;
    
    // 设置宠物选择
    document.querySelectorAll('.pet-option').forEach(option => {
      option.classList.remove('active');
      if (option.dataset.pet === this.settings.petType) {
        option.classList.add('active');
      }
    });
    
    // 设置滑块值
    this.updateSlider('speedSlider', this.settings.speed, (val) => `${val}x`);
    this.updateSlider('opacitySlider', this.settings.opacity, (val) => `${val}%`);
    this.updateSlider('sizeSlider', this.settings.size, (val) => `${val}px`);
    this.updateSlider('sensitivitySlider', this.settings.sensitivity, (val) => val.toString());
    
    // 设置复选框
    document.getElementById('clickAnimationToggle').checked = this.settings.clickAnimation;
    document.getElementById('hoverAnimationToggle').checked = this.settings.hoverAnimation;
    document.getElementById('performanceModeToggle').checked = this.settings.performanceMode;

    // 初始化网站管理
    this.initSiteManagement();
  }

  updateSlider(sliderId, value, formatter) {
    const slider = document.getElementById(sliderId);
    const valueDisplay = slider.parentNode.querySelector('.slider-value');
    
    slider.value = value;
    valueDisplay.textContent = formatter(value);
  }

  bindEvents() {
    // 开关切换
    document.getElementById('enableToggle').addEventListener('change', (e) => {
      this.settings.enabled = e.target.checked;
      this.updatePreview();
    });

    // 宠物选择
    document.querySelectorAll('.pet-option').forEach(option => {
      option.addEventListener('click', () => {
        document.querySelectorAll('.pet-option').forEach(opt => opt.classList.remove('active'));
        option.classList.add('active');
        this.settings.petType = option.dataset.pet;
        this.updatePreview();
      });
    });

    // 滑块事件
    this.bindSliderEvent('speedSlider', 'speed', (val) => `${val}x`);
    this.bindSliderEvent('opacitySlider', 'opacity', (val) => `${val}%`);
    this.bindSliderEvent('sizeSlider', 'size', (val) => `${val}px`);
    this.bindSliderEvent('sensitivitySlider', 'sensitivity', (val) => val.toString());

    // 复选框事件
    document.getElementById('clickAnimationToggle').addEventListener('change', (e) => {
      this.settings.clickAnimation = e.target.checked;
    });

    document.getElementById('hoverAnimationToggle').addEventListener('change', (e) => {
      this.settings.hoverAnimation = e.target.checked;
    });

    document.getElementById('performanceModeToggle').addEventListener('change', (e) => {
      this.settings.performanceMode = e.target.checked;
    });

    // 网站管理事件
    document.getElementById('toggleCurrentSite').addEventListener('click', () => {
      this.toggleCurrentSite();
    });

    // 按钮事件
    document.getElementById('resetButton').addEventListener('click', () => {
      this.resetSettings();
    });

    document.getElementById('saveButton').addEventListener('click', () => {
      this.saveSettings();
    });

    // 预览区域鼠标事件
    const previewArea = document.getElementById('previewArea');
    previewArea.addEventListener('mousemove', (e) => {
      this.updatePreviewPosition(e);
    });
  }

  bindSliderEvent(sliderId, settingKey, formatter) {
    const slider = document.getElementById(sliderId);
    const valueDisplay = slider.parentNode.querySelector('.slider-value');
    
    slider.addEventListener('input', (e) => {
      const value = parseFloat(e.target.value);
      this.settings[settingKey] = value;
      valueDisplay.textContent = formatter(value);
      this.updatePreview();
    });
  }

  async initPreview() {
    // 等待雪碧图管理器加载
    await this.loadSpriteManager();

    this.updatePreview();

    // 启动预览动画
    this.startPreviewAnimation();
  }

  async loadSpriteManager() {
    // 动态加载雪碧图管理器
    if (!window.SpriteManager) {
      return new Promise((resolve) => {
        const script = document.createElement('script');
        script.src = chrome.runtime.getURL('utils/sprite-manager.js');
        document.head.appendChild(script);

        script.onload = () => resolve();
        script.onerror = () => resolve(); // 即使加载失败也继续

        // 超时保护
        setTimeout(resolve, 2000);
      });
    }
  }

  async initSiteManagement() {
    try {
      // 获取当前标签页信息
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      if (tab && tab.url) {
        const url = new URL(tab.url);
        const hostname = url.hostname;

        document.getElementById('currentSiteUrl').textContent = hostname;

        // 检查当前网站是否被禁用
        const isDisabled = this.settings.disabledSites.includes(hostname);
        const toggleBtn = document.getElementById('toggleCurrentSite');

        toggleBtn.textContent = isDisabled ? '启用' : '禁用';
        toggleBtn.className = `btn btn-small ${isDisabled ? 'btn-primary' : 'btn-secondary'}`;
      }

      // 更新禁用网站列表
      this.updateDisabledSitesList();

    } catch (error) {
      console.error('初始化网站管理失败:', error);
      document.getElementById('currentSiteUrl').textContent = '无法获取当前网站';
    }
  }

  async toggleCurrentSite() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      if (!tab || !tab.url) return;

      const url = new URL(tab.url);
      const hostname = url.hostname;

      const isCurrentlyDisabled = this.settings.disabledSites.includes(hostname);

      if (isCurrentlyDisabled) {
        // 启用网站
        this.settings.disabledSites = this.settings.disabledSites.filter(site => site !== hostname);
      } else {
        // 禁用网站
        this.settings.disabledSites.push(hostname);
      }

      // 更新UI
      const toggleBtn = document.getElementById('toggleCurrentSite');
      toggleBtn.textContent = isCurrentlyDisabled ? '禁用' : '启用';
      toggleBtn.className = `btn btn-small ${isCurrentlyDisabled ? 'btn-secondary' : 'btn-primary'}`;

      // 更新禁用网站列表
      this.updateDisabledSitesList();

      // 自动保存设置
      await this.saveSettings();

    } catch (error) {
      console.error('切换网站状态失败:', error);
    }
  }

  updateDisabledSitesList() {
    const listContainer = document.getElementById('disabledSitesList');

    if (this.settings.disabledSites.length === 0) {
      listContainer.innerHTML = '<div class="empty-sites-message">暂无禁用的网站</div>';
      return;
    }

    listContainer.innerHTML = '';

    this.settings.disabledSites.forEach(site => {
      const item = document.createElement('div');
      item.className = 'disabled-site-item';

      item.innerHTML = `
        <span class="disabled-site-url">${site}</span>
        <button class="remove-site-btn" data-site="${site}">移除</button>
      `;

      // 绑定移除按钮事件
      const removeBtn = item.querySelector('.remove-site-btn');
      removeBtn.addEventListener('click', () => {
        this.removeSiteFromDisabledList(site);
      });

      listContainer.appendChild(item);
    });
  }

  async removeSiteFromDisabledList(site) {
    this.settings.disabledSites = this.settings.disabledSites.filter(s => s !== site);

    // 更新UI
    this.updateDisabledSitesList();

    // 如果移除的是当前网站，更新切换按钮
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tab && tab.url) {
        const currentHostname = new URL(tab.url).hostname;
        if (currentHostname === site) {
          const toggleBtn = document.getElementById('toggleCurrentSite');
          toggleBtn.textContent = '禁用';
          toggleBtn.className = 'btn btn-small btn-secondary';
        }
      }
    } catch (error) {
      console.error('更新当前网站状态失败:', error);
    }

    // 自动保存设置
    await this.saveSettings();
  }

  updatePreview() {
    const previewPet = document.getElementById('previewPet');

    // 清空之前的内容
    previewPet.innerHTML = '';

    if (!this.settings.enabled) {
      previewPet.style.display = 'none';
      return;
    }

    previewPet.style.display = 'block';

    // 使用雪碧图管理器创建预览元素
    if (window.SpriteManager) {
      try {
        const petElement = window.SpriteManager.createElement(
          this.settings.petType,
          this.settings.size
        );

        petElement.style.position = 'relative';
        petElement.style.opacity = this.settings.opacity / 100;

        previewPet.appendChild(petElement);

        // 创建动画器
        if (this.previewAnimator) {
          this.previewAnimator.cleanup();
        }

        this.previewAnimator = window.SpriteManager.createAnimator(
          petElement,
          this.settings.petType
        );

        // 设置动画速度
        this.previewAnimator.setSpeed(this.settings.speed);

        return;
      } catch (error) {
        console.warn('使用雪碧图管理器失败，使用降级方案', error);
      }
    }

    // 降级方案：使用简单的CSS样式
    this.createFallbackPreview(previewPet);
  }

  createFallbackPreview(container) {
    const petColors = {
      cat: '#ff6b6b',
      dog: '#4ecdc4',
      rabbit: '#45b7d1',
      bird: '#96ceb4',
      panda: '#feca57'
    };

    container.style.backgroundColor = petColors[this.settings.petType];
    container.style.width = `${this.settings.size}px`;
    container.style.height = `${this.settings.size}px`;
    container.style.opacity = this.settings.opacity / 100;
    container.style.borderRadius = '50%';
    container.style.position = 'relative';

    // 添加简单的眼睛效果
    container.innerHTML = `
      <div style="
        position: absolute;
        top: 25%;
        left: 25%;
        width: 20%;
        height: 20%;
        background: white;
        border-radius: 50%;
        box-shadow: 30% 0 0 white;
      "></div>
      <div style="
        position: absolute;
        bottom: 25%;
        left: 50%;
        width: 25%;
        height: 10%;
        background: rgba(0,0,0,0.6);
        border-radius: 50%;
        transform: translateX(-50%);
      "></div>
    `;
  }

  startPreviewAnimation() {
    // 启动预览区域的动画效果
    if (this.previewAnimator) {
      this.previewAnimator.play('idle');
    } else {
      // 降级方案：CSS动画
      const previewPet = document.getElementById('previewPet');
      previewPet.style.animation = 'pulse 2s ease-in-out infinite';
    }
  }

  updatePreviewPosition(e) {
    if (!this.settings.enabled) return;
    
    const previewArea = document.getElementById('previewArea');
    const previewPet = document.getElementById('previewPet');
    const rect = previewArea.getBoundingClientRect();
    
    const x = e.clientX - rect.left - this.settings.size / 2;
    const y = e.clientY - rect.top - this.settings.size / 2;
    
    // 限制在预览区域内
    const maxX = previewArea.offsetWidth - this.settings.size;
    const maxY = previewArea.offsetHeight - this.settings.size;
    
    const clampedX = Math.max(0, Math.min(x, maxX));
    const clampedY = Math.max(0, Math.min(y, maxY));
    
    previewPet.style.left = `${clampedX}px`;
    previewPet.style.top = `${clampedY}px`;
  }

  resetSettings() {
    // 重置为默认设置
    this.settings = {
      enabled: true,
      petType: 'cat',
      speed: 1.0,
      opacity: 100,
      size: 32,
      sensitivity: 0.1,
      clickAnimation: true,
      hoverAnimation: true
    };
    
    // 更新UI
    this.initializeUI();
    this.updatePreview();
    
    // 显示重置提示
    this.showResetSuccess();
  }

  showSaveSuccess() {
    const saveButton = document.getElementById('saveButton');
    const originalText = saveButton.textContent;
    
    saveButton.textContent = '已保存!';
    saveButton.style.background = '#28a745';
    
    setTimeout(() => {
      saveButton.textContent = originalText;
      saveButton.style.background = '';
    }, 1500);
  }

  showResetSuccess() {
    const resetButton = document.getElementById('resetButton');
    const originalText = resetButton.textContent;
    
    resetButton.textContent = '已重置!';
    resetButton.style.background = '#17a2b8';
    resetButton.style.color = 'white';
    
    setTimeout(() => {
      resetButton.textContent = originalText;
      resetButton.style.background = '';
      resetButton.style.color = '';
    }, 1500);
  }
}

// 当DOM加载完成时初始化
document.addEventListener('DOMContentLoaded', () => {
  new PopupController();
});

// 处理来自background的消息
chrome.runtime.onMessage.addListener((message) => {
  if (message.type === 'POPUP_OPENED') {
    // 可以在这里处理popup打开时的逻辑
    console.log('Popup opened');
  }
});
