// Pet Cursor 性能监控器
class PerformanceMonitor {
  constructor() {
    this.isEnabled = false;
    this.metrics = {
      fps: 0,
      frameTime: 0,
      memoryUsage: 0,
      animationCount: 0,
      lastFrameTime: 0,
      frameCount: 0,
      startTime: performance.now()
    };
    
    this.thresholds = {
      fps: { good: 50, warning: 30 },
      frameTime: { good: 16, warning: 33 }, // ms
      memory: { good: 50, warning: 100 } // MB
    };
    
    this.callbacks = {
      onPerformanceIssue: null,
      onMetricsUpdate: null
    };
    
    this.monitoringInterval = null;
    this.frameMonitorId = null;
  }

  start() {
    if (this.isEnabled) return;
    
    this.isEnabled = true;
    this.metrics.startTime = performance.now();
    this.metrics.frameCount = 0;
    
    // 启动帧率监控
    this.startFrameMonitoring();
    
    // 启动定期监控
    this.monitoringInterval = setInterval(() => {
      this.updateMetrics();
      this.checkPerformance();
    }, 1000);
    
    console.log('Pet Cursor 性能监控已启动');
  }

  stop() {
    if (!this.isEnabled) return;
    
    this.isEnabled = false;
    
    if (this.frameMonitorId) {
      cancelAnimationFrame(this.frameMonitorId);
      this.frameMonitorId = null;
    }
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    
    console.log('Pet Cursor 性能监控已停止');
  }

  startFrameMonitoring() {
    let lastTime = performance.now();
    
    const monitor = (currentTime) => {
      if (!this.isEnabled) return;
      
      // 计算帧时间
      const frameTime = currentTime - lastTime;
      this.metrics.frameTime = frameTime;
      this.metrics.lastFrameTime = currentTime;
      this.metrics.frameCount++;
      
      lastTime = currentTime;
      this.frameMonitorId = requestAnimationFrame(monitor);
    };
    
    this.frameMonitorId = requestAnimationFrame(monitor);
  }

  updateMetrics() {
    const currentTime = performance.now();
    const elapsed = currentTime - this.metrics.startTime;
    
    // 计算FPS
    this.metrics.fps = Math.round((this.metrics.frameCount * 1000) / elapsed);
    
    // 获取内存使用情况（如果可用）
    if (performance.memory) {
      this.metrics.memoryUsage = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
    }
    
    // 统计动画元素数量
    this.metrics.animationCount = document.querySelectorAll('.pet-cursor').length;
    
    // 触发回调
    if (this.callbacks.onMetricsUpdate) {
      this.callbacks.onMetricsUpdate(this.getMetrics());
    }
  }

  checkPerformance() {
    const issues = [];
    
    // 检查FPS
    if (this.metrics.fps < this.thresholds.fps.warning) {
      issues.push({
        type: 'fps',
        severity: this.metrics.fps < this.thresholds.fps.warning ? 'error' : 'warning',
        message: `FPS过低: ${this.metrics.fps}`,
        suggestion: '考虑启用性能模式或减少动画复杂度'
      });
    }
    
    // 检查帧时间
    if (this.metrics.frameTime > this.thresholds.frameTime.warning) {
      issues.push({
        type: 'frameTime',
        severity: this.metrics.frameTime > this.thresholds.frameTime.warning ? 'error' : 'warning',
        message: `帧时间过长: ${this.metrics.frameTime.toFixed(2)}ms`,
        suggestion: '动画计算可能过于复杂'
      });
    }
    
    // 检查内存使用
    if (this.metrics.memoryUsage > this.thresholds.memory.warning) {
      issues.push({
        type: 'memory',
        severity: this.metrics.memoryUsage > this.thresholds.memory.warning ? 'error' : 'warning',
        message: `内存使用过高: ${this.metrics.memoryUsage}MB`,
        suggestion: '可能存在内存泄漏'
      });
    }
    
    // 检查动画元素数量
    if (this.metrics.animationCount > 5) {
      issues.push({
        type: 'animationCount',
        severity: 'warning',
        message: `动画元素过多: ${this.metrics.animationCount}`,
        suggestion: '检查是否有重复创建的动画元素'
      });
    }
    
    // 触发性能问题回调
    if (issues.length > 0 && this.callbacks.onPerformanceIssue) {
      this.callbacks.onPerformanceIssue(issues);
    }
  }

  getMetrics() {
    return { ...this.metrics };
  }

  getPerformanceLevel() {
    const { fps, frameTime, memoryUsage } = this.metrics;
    
    if (fps >= this.thresholds.fps.good && 
        frameTime <= this.thresholds.frameTime.good && 
        memoryUsage <= this.thresholds.memory.good) {
      return 'good';
    } else if (fps >= this.thresholds.fps.warning && 
               frameTime <= this.thresholds.frameTime.warning && 
               memoryUsage <= this.thresholds.memory.warning) {
      return 'warning';
    } else {
      return 'error';
    }
  }

  setCallback(type, callback) {
    if (this.callbacks.hasOwnProperty(type)) {
      this.callbacks[type] = callback;
    }
  }

  // 性能优化建议
  getOptimizationSuggestions() {
    const suggestions = [];
    const level = this.getPerformanceLevel();
    
    if (level === 'error' || level === 'warning') {
      suggestions.push({
        title: '启用性能模式',
        description: '减少动画复杂度和帧率',
        action: 'enablePerformanceMode'
      });
      
      if (this.metrics.animationCount > 1) {
        suggestions.push({
          title: '减少动画元素',
          description: '检查并移除重复的动画元素',
          action: 'cleanupAnimations'
        });
      }
      
      if (this.metrics.memoryUsage > this.thresholds.memory.good) {
        suggestions.push({
          title: '清理内存',
          description: '释放未使用的资源',
          action: 'cleanupMemory'
        });
      }
    }
    
    return suggestions;
  }

  // 自动优化
  autoOptimize() {
    const level = this.getPerformanceLevel();
    
    if (level === 'error') {
      console.warn('Pet Cursor: 检测到性能问题，启用自动优化');
      
      // 启用性能模式
      this.enablePerformanceMode();
      
      // 清理重复元素
      this.cleanupDuplicateAnimations();
      
      return true;
    }
    
    return false;
  }

  enablePerformanceMode() {
    // 通知content script启用性能模式
    if (typeof chrome !== 'undefined' && chrome.tabs) {
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs[0]) {
          chrome.tabs.sendMessage(tabs[0].id, {
            type: 'ENABLE_PERFORMANCE_MODE'
          });
        }
      });
    }
    
    // 如果在content script中，直接应用
    if (window.petCursor) {
      window.petCursor.enablePerformanceMode();
    }
  }

  cleanupDuplicateAnimations() {
    const petElements = document.querySelectorAll('.pet-cursor');
    
    if (petElements.length > 1) {
      // 保留第一个，移除其他的
      for (let i = 1; i < petElements.length; i++) {
        if (petElements[i].parentNode) {
          petElements[i].parentNode.removeChild(petElements[i]);
        }
      }
      
      console.log(`Pet Cursor: 清理了 ${petElements.length - 1} 个重复的动画元素`);
    }
  }

  // 生成性能报告
  generateReport() {
    const metrics = this.getMetrics();
    const level = this.getPerformanceLevel();
    const suggestions = this.getOptimizationSuggestions();
    
    return {
      timestamp: new Date().toISOString(),
      performanceLevel: level,
      metrics: metrics,
      suggestions: suggestions,
      summary: {
        fps: `${metrics.fps} FPS`,
        frameTime: `${metrics.frameTime.toFixed(2)}ms`,
        memory: `${metrics.memoryUsage}MB`,
        animations: `${metrics.animationCount} 个动画`
      }
    };
  }
}

// 性能优化工具
class PerformanceOptimizer {
  static throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  static debounce(func, delay) {
    let timeoutId;
    return function() {
      const args = arguments;
      const context = this;
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(context, args), delay);
    };
  }

  static requestIdleCallback(callback, options = {}) {
    if (window.requestIdleCallback) {
      return window.requestIdleCallback(callback, options);
    } else {
      // 降级方案
      return setTimeout(() => {
        callback({
          didTimeout: false,
          timeRemaining: () => 50
        });
      }, 1);
    }
  }

  static cancelIdleCallback(id) {
    if (window.cancelIdleCallback) {
      window.cancelIdleCallback(id);
    } else {
      clearTimeout(id);
    }
  }

  // 检查设备性能等级
  static getDevicePerformanceLevel() {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    
    if (!gl) return 'low';
    
    const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
    if (debugInfo) {
      const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
      
      // 简单的GPU性能判断
      if (renderer.includes('Intel HD') || renderer.includes('Intel UHD')) {
        return 'low';
      } else if (renderer.includes('GTX') || renderer.includes('RTX') || renderer.includes('Radeon')) {
        return 'high';
      }
    }
    
    // 基于CPU核心数判断
    const cores = navigator.hardwareConcurrency || 2;
    if (cores >= 8) return 'high';
    if (cores >= 4) return 'medium';
    return 'low';
  }

  // 自适应性能设置
  static getAdaptiveSettings() {
    const level = this.getDevicePerformanceLevel();
    
    const settings = {
      low: {
        maxFPS: 30,
        animationQuality: 'low',
        enableHardwareAcceleration: false,
        maxAnimations: 1
      },
      medium: {
        maxFPS: 45,
        animationQuality: 'medium',
        enableHardwareAcceleration: true,
        maxAnimations: 2
      },
      high: {
        maxFPS: 60,
        animationQuality: 'high',
        enableHardwareAcceleration: true,
        maxAnimations: 3
      }
    };
    
    return settings[level];
  }
}

// 导出
window.PerformanceMonitor = PerformanceMonitor;
window.PerformanceOptimizer = PerformanceOptimizer;
