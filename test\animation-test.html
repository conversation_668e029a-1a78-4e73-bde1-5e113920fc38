<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pet Cursor 动画测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }

        .test-section h2 {
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .pet-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .pet-card {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            transition: transform 0.3s ease;
        }

        .pet-card:hover {
            transform: translateY(-5px);
        }

        .pet-preview {
            width: 64px;
            height: 64px;
            margin: 0 auto 15px;
            border-radius: 50%;
            position: relative;
            cursor: pointer;
        }

        .pet-name {
            font-size: 1.1em;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .pet-status {
            font-size: 0.9em;
            opacity: 0.8;
        }

        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .control-group label {
            font-size: 0.9em;
            font-weight: 500;
        }

        .control-group input, .control-group select, .control-group button {
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 0.9em;
        }

        .control-group input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .control-group button {
            background: rgba(255, 255, 255, 0.3);
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .control-group button:hover {
            background: rgba(255, 255, 255, 0.4);
        }

        .animation-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .animation-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            background: rgba(255, 154, 158, 0.8);
            color: white;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .animation-btn:hover {
            background: rgba(255, 154, 158, 1);
            transform: translateY(-2px);
        }

        .animation-btn.active {
            background: #FF6B6B;
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
        }

        .info-panel {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }

        .info-panel h3 {
            margin-top: 0;
            margin-bottom: 15px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .test-area {
            height: 300px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            position: relative;
            overflow: hidden;
            margin: 20px 0;
            border: 2px dashed rgba(255, 255, 255, 0.3);
        }

        .test-area-label {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.2em;
            opacity: 0.6;
            pointer-events: none;
        }

        .performance-monitor {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.8em;
            min-width: 200px;
        }

        .performance-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .status-good { color: #4CAF50; }
        .status-warning { color: #FF9800; }
        .status-error { color: #F44336; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐾 Pet Cursor 动画测试</h1>
            <p>测试可爱动物的各种动画效果和性能表现</p>
        </div>

        <!-- 宠物展示区 -->
        <div class="test-section">
            <h2>🎭 宠物动画展示</h2>
            <div class="pet-showcase" id="petShowcase">
                <!-- 动态生成宠物卡片 -->
            </div>
        </div>

        <!-- 动画控制区 -->
        <div class="test-section">
            <h2>🎮 动画控制</h2>
            <div class="controls">
                <div class="control-group">
                    <label>选择宠物</label>
                    <select id="petSelect">
                        <option value="cat">小猫咪</option>
                        <option value="dog">小狗狗</option>
                        <option value="rabbit">小兔子</option>
                        <option value="bird">小鸟</option>
                        <option value="panda">小熊猫</option>
                    </select>
                </div>
                <div class="control-group">
                    <label>动画速度</label>
                    <input type="range" id="speedSlider" min="0.1" max="3" step="0.1" value="1">
                </div>
                <div class="control-group">
                    <label>宠物大小</label>
                    <input type="range" id="sizeSlider" min="24" max="128" step="8" value="64">
                </div>
                <div class="control-group">
                    <label>透明度</label>
                    <input type="range" id="opacitySlider" min="0.1" max="1" step="0.1" value="1">
                </div>
            </div>

            <div class="animation-buttons">
                <button class="animation-btn" data-animation="idle">静止</button>
                <button class="animation-btn" data-animation="walk">移动</button>
                <button class="animation-btn" data-animation="click">点击</button>
                <button class="animation-btn" data-animation="hover">悬停</button>
            </div>
        </div>

        <!-- 测试区域 -->
        <div class="test-section">
            <h2>🎯 交互测试区</h2>
            <div class="test-area" id="testArea">
                <div class="test-area-label">移动鼠标测试跟随效果</div>
            </div>
        </div>

        <!-- 信息面板 -->
        <div class="test-section">
            <div class="info-panel">
                <h3>📊 动画信息</h3>
                <div class="info-grid" id="infoGrid">
                    <!-- 动态生成信息项 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 性能监控 -->
    <div class="performance-monitor" id="performanceMonitor">
        <div class="performance-item">
            <span>FPS:</span>
            <span id="fpsCounter" class="status-good">60</span>
        </div>
        <div class="performance-item">
            <span>内存:</span>
            <span id="memoryUsage" class="status-good">--</span>
        </div>
        <div class="performance-item">
            <span>动画状态:</span>
            <span id="animationStatus" class="status-good">idle</span>
        </div>
        <div class="performance-item">
            <span>当前帧:</span>
            <span id="currentFrame" class="status-good">0</span>
        </div>
    </div>

    <!-- 加载脚本 -->
    <script src="../utils/sprite-manager.js"></script>
    <script>
        // 动画测试控制器
        class AnimationTester {
            constructor() {
                this.currentPet = null;
                this.animator = null;
                this.stateMachine = null;
                this.performanceMonitor = null;
                
                this.init();
            }

            async init() {
                // 等待SpriteManager加载
                await this.waitForSpriteManager();
                
                // 初始化UI
                this.initUI();
                
                // 创建默认宠物
                this.createPet('cat');
                
                // 绑定事件
                this.bindEvents();
                
                // 启动性能监控
                this.startPerformanceMonitoring();
            }

            async waitForSpriteManager() {
                return new Promise((resolve) => {
                    const checkInterval = setInterval(() => {
                        if (window.SpriteManager) {
                            clearInterval(checkInterval);
                            resolve();
                        }
                    }, 100);
                });
            }

            initUI() {
                this.createPetShowcase();
                this.updateInfoPanel();
            }

            createPetShowcase() {
                const showcase = document.getElementById('petShowcase');
                const petTypes = ['cat', 'dog', 'rabbit', 'bird', 'panda'];
                const petNames = ['小猫咪', '小狗狗', '小兔子', '小鸟', '小熊猫'];

                petTypes.forEach((petType, index) => {
                    const card = document.createElement('div');
                    card.className = 'pet-card';
                    card.innerHTML = `
                        <div class="pet-preview pet-preview-${petType}" data-pet="${petType}"></div>
                        <div class="pet-name">${petNames[index]}</div>
                        <div class="pet-status">点击预览</div>
                    `;
                    showcase.appendChild(card);

                    // 创建预览宠物
                    const preview = card.querySelector('.pet-preview');
                    const previewElement = window.SpriteManager.createElement(petType, 64);
                    preview.appendChild(previewElement);

                    // 绑定点击事件
                    card.addEventListener('click', () => {
                        this.createPet(petType);
                        document.getElementById('petSelect').value = petType;
                    });
                });
            }

            createPet(petType) {
                // 清理之前的宠物
                this.cleanup();

                // 创建新宠物
                this.currentPet = window.SpriteManager.createElement(petType, 64);
                this.currentPet.style.position = 'absolute';
                this.currentPet.style.left = '50%';
                this.currentPet.style.top = '50%';
                this.currentPet.style.transform = 'translate(-50%, -50%)';

                // 添加到测试区域
                const testArea = document.getElementById('testArea');
                testArea.appendChild(this.currentPet);

                // 创建动画器和状态机
                this.animator = window.SpriteManager.createAnimator(this.currentPet, petType);
                this.stateMachine = window.SpriteManager.createStateMachine(this.animator);

                // 更新信息面板
                this.updateInfoPanel();
            }

            bindEvents() {
                // 动画按钮
                document.querySelectorAll('.animation-btn').forEach(btn => {
                    btn.addEventListener('click', () => {
                        const animation = btn.dataset.animation;
                        this.playAnimation(animation);
                        
                        // 更新按钮状态
                        document.querySelectorAll('.animation-btn').forEach(b => b.classList.remove('active'));
                        btn.classList.add('active');
                    });
                });

                // 控制滑块
                document.getElementById('speedSlider').addEventListener('input', (e) => {
                    if (this.animator) {
                        this.animator.setSpeed(parseFloat(e.target.value));
                    }
                });

                document.getElementById('sizeSlider').addEventListener('input', (e) => {
                    if (this.animator) {
                        this.animator.setSize(parseInt(e.target.value));
                    }
                });

                document.getElementById('opacitySlider').addEventListener('input', (e) => {
                    if (this.currentPet) {
                        this.currentPet.style.opacity = e.target.value;
                    }
                });

                // 宠物选择
                document.getElementById('petSelect').addEventListener('change', (e) => {
                    this.createPet(e.target.value);
                });

                // 测试区域鼠标事件
                const testArea = document.getElementById('testArea');
                testArea.addEventListener('mousemove', (e) => {
                    if (this.currentPet) {
                        const rect = testArea.getBoundingClientRect();
                        const x = e.clientX - rect.left - 32;
                        const y = e.clientY - rect.top - 32;
                        
                        this.currentPet.style.left = `${x}px`;
                        this.currentPet.style.top = `${y}px`;
                        this.currentPet.style.transform = 'none';
                        
                        // 触发移动动画
                        if (this.stateMachine) {
                            this.stateMachine.setState('walk');
                        }
                    }
                });
            }

            playAnimation(animationName) {
                if (this.stateMachine) {
                    this.stateMachine.setState(animationName, { force: true });
                }
            }

            updateInfoPanel() {
                const infoGrid = document.getElementById('infoGrid');
                const info = {
                    '当前宠物': this.currentPet ? document.getElementById('petSelect').value : '无',
                    '动画状态': this.stateMachine ? this.stateMachine.getCurrentState() : '无',
                    '当前帧': this.animator ? this.animator.getCurrentFrame() : '0',
                    '播放状态': this.animator ? (this.animator.isAnimationPlaying() ? '播放中' : '暂停') : '无',
                    '动画速度': document.getElementById('speedSlider').value + 'x',
                    '宠物大小': document.getElementById('sizeSlider').value + 'px'
                };

                infoGrid.innerHTML = '';
                Object.entries(info).forEach(([key, value]) => {
                    const item = document.createElement('div');
                    item.className = 'info-item';
                    item.innerHTML = `<span>${key}:</span><span>${value}</span>`;
                    infoGrid.appendChild(item);
                });
            }

            startPerformanceMonitoring() {
                let frameCount = 0;
                let lastTime = performance.now();

                const monitor = () => {
                    frameCount++;
                    const currentTime = performance.now();
                    
                    if (currentTime - lastTime >= 1000) {
                        const fps = Math.round(frameCount * 1000 / (currentTime - lastTime));
                        document.getElementById('fpsCounter').textContent = fps;
                        document.getElementById('fpsCounter').className = fps >= 50 ? 'status-good' : fps >= 30 ? 'status-warning' : 'status-error';
                        
                        frameCount = 0;
                        lastTime = currentTime;
                    }

                    // 更新其他信息
                    if (this.stateMachine) {
                        document.getElementById('animationStatus').textContent = this.stateMachine.getCurrentState();
                    }
                    
                    if (this.animator) {
                        document.getElementById('currentFrame').textContent = this.animator.getCurrentFrame();
                    }

                    // 更新信息面板
                    this.updateInfoPanel();

                    requestAnimationFrame(monitor);
                };

                monitor();
            }

            cleanup() {
                if (this.animator) {
                    this.animator.cleanup();
                    this.animator = null;
                }
                
                if (this.stateMachine) {
                    this.stateMachine.cleanup();
                    this.stateMachine = null;
                }
                
                if (this.currentPet && this.currentPet.parentNode) {
                    this.currentPet.parentNode.removeChild(this.currentPet);
                    this.currentPet = null;
                }
            }
        }

        // 启动测试器
        document.addEventListener('DOMContentLoaded', () => {
            new AnimationTester();
        });
    </script>
</body>
</html>
