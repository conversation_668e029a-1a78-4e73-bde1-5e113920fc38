// Pet Cursor 兼容性检查器
class CompatibilityChecker {
  constructor() {
    this.issues = [];
    this.fixes = [];
    this.siteSpecificRules = new Map();
    
    this.initSiteSpecificRules();
  }

  initSiteSpecificRules() {
    // 常见网站的特殊处理规则
    this.siteSpecificRules.set('youtube.com', {
      zIndexIssues: true,
      fullscreenConflict: true,
      customCursor: true,
      fixes: ['adjustZIndex', 'handleFullscreen', 'respectCustomCursor']
    });
    
    this.siteSpecificRules.set('github.com', {
      darkTheme: true,
      codeEditor: true,
      fixes: ['adjustForDarkTheme', 'avoidCodeEditor']
    });
    
    this.siteSpecificRules.set('google.com', {
      searchOverlay: true,
      imageSearch: true,
      fixes: ['avoidSearchOverlay', 'handleImageSearch']
    });
    
    this.siteSpecificRules.set('facebook.com', {
      infiniteScroll: true,
      videoPlayer: true,
      fixes: ['handleInfiniteScroll', 'avoidVideoPlayer']
    });
    
    this.siteSpecificRules.set('twitter.com', {
      infiniteScroll: true,
      modalDialogs: true,
      fixes: ['handleInfiniteScroll', 'respectModalDialogs']
    });
    
    this.siteSpecificRules.set('reddit.com', {
      infiniteScroll: true,
      expandableContent: true,
      fixes: ['handleInfiniteScroll', 'handleExpandableContent']
    });
  }

  async checkCompatibility() {
    this.issues = [];
    this.fixes = [];
    
    // 检查基础浏览器兼容性
    this.checkBrowserSupport();
    
    // 检查CSS兼容性
    this.checkCSSSupport();
    
    // 检查当前网站特定问题
    await this.checkSiteSpecificIssues();
    
    // 检查样式冲突
    this.checkStyleConflicts();
    
    // 检查性能相关问题
    this.checkPerformanceIssues();
    
    return {
      issues: this.issues,
      fixes: this.fixes,
      compatible: this.issues.filter(issue => issue.severity === 'error').length === 0
    };
  }

  checkBrowserSupport() {
    const userAgent = navigator.userAgent;
    
    // 检查浏览器版本
    if (userAgent.includes('Chrome/')) {
      const version = parseInt(userAgent.match(/Chrome\/(\d+)/)[1]);
      if (version < 88) {
        this.addIssue('browser', 'error', 'Chrome版本过低', '需要Chrome 88或更高版本');
      }
    } else if (userAgent.includes('Firefox/')) {
      const version = parseInt(userAgent.match(/Firefox\/(\d+)/)[1]);
      if (version < 85) {
        this.addIssue('browser', 'error', 'Firefox版本过低', '需要Firefox 85或更高版本');
      }
    } else if (userAgent.includes('Edge/')) {
      const version = parseInt(userAgent.match(/Edge\/(\d+)/)[1]);
      if (version < 88) {
        this.addIssue('browser', 'error', 'Edge版本过低', '需要Edge 88或更高版本');
      }
    }
    
    // 检查必要的API支持
    if (!window.requestAnimationFrame) {
      this.addIssue('api', 'error', '不支持requestAnimationFrame', '动画可能不流畅');
      this.addFix('polyfillRequestAnimationFrame');
    }
    
    if (!window.performance) {
      this.addIssue('api', 'warning', '不支持Performance API', '无法进行性能监控');
    }
    
    if (!document.querySelector) {
      this.addIssue('api', 'error', '不支持querySelector', '无法正常工作');
    }
  }

  checkCSSSupport() {
    const testElement = document.createElement('div');
    
    // 检查transform支持
    if (!('transform' in testElement.style)) {
      this.addIssue('css', 'error', '不支持CSS Transform', '动画无法正常显示');
      this.addFix('fallbackToPositioning');
    }
    
    // 检查will-change支持
    if (!('willChange' in testElement.style)) {
      this.addIssue('css', 'warning', '不支持will-change', '可能影响动画性能');
    }
    
    // 检查backdrop-filter支持
    if (!('backdropFilter' in testElement.style)) {
      this.addIssue('css', 'info', '不支持backdrop-filter', '某些视觉效果可能不可用');
    }
    
    // 检查CSS变量支持
    if (!CSS.supports('color', 'var(--test)')) {
      this.addIssue('css', 'warning', '不支持CSS变量', '主题功能可能受限');
    }
  }

  async checkSiteSpecificIssues() {
    const hostname = window.location.hostname;
    const rules = this.siteSpecificRules.get(hostname);
    
    if (!rules) return;
    
    // 检查z-index冲突
    if (rules.zIndexIssues) {
      const highZIndexElements = document.querySelectorAll('[style*="z-index"]');
      let maxZIndex = 0;
      
      highZIndexElements.forEach(el => {
        const zIndex = parseInt(el.style.zIndex) || 0;
        if (zIndex > maxZIndex) maxZIndex = zIndex;
      });
      
      if (maxZIndex > 2147483647) {
        this.addIssue('zindex', 'error', 'z-index值过高', '可能导致显示问题');
        this.addFix('adjustZIndex');
      }
    }
    
    // 检查全屏冲突
    if (rules.fullscreenConflict) {
      const fullscreenElements = document.querySelectorAll('[data-fullscreen], .fullscreen, .theater-mode');
      if (fullscreenElements.length > 0) {
        this.addIssue('fullscreen', 'warning', '检测到全屏元素', '可能在全屏模式下显示异常');
        this.addFix('handleFullscreen');
      }
    }
    
    // 检查自定义光标
    if (rules.customCursor) {
      const bodyStyle = getComputedStyle(document.body);
      if (bodyStyle.cursor !== 'auto' && bodyStyle.cursor !== 'default') {
        this.addIssue('cursor', 'info', '网站使用自定义光标', '可能与宠物光标产生视觉冲突');
        this.addFix('respectCustomCursor');
      }
    }
    
    // 检查无限滚动
    if (rules.infiniteScroll) {
      const scrollContainers = document.querySelectorAll('[data-scroll], .infinite-scroll, .virtualized');
      if (scrollContainers.length > 0) {
        this.addIssue('scroll', 'info', '检测到无限滚动', '可能影响性能');
        this.addFix('handleInfiniteScroll');
      }
    }
  }

  checkStyleConflicts() {
    // 检查可能冲突的CSS类名
    const conflictingClasses = ['.pet-cursor', '.cursor', '.mouse-follower'];
    
    conflictingClasses.forEach(className => {
      const existingElements = document.querySelectorAll(className);
      if (existingElements.length > 0) {
        this.addIssue('css-conflict', 'warning', `检测到冲突的CSS类: ${className}`, '可能影响样式显示');
        this.addFix('useUniqueClassNames');
      }
    });
    
    // 检查全局CSS重置
    const bodyStyle = getComputedStyle(document.body);
    if (bodyStyle.margin === '0px' && bodyStyle.padding === '0px') {
      // 可能使用了CSS重置，需要确保我们的样式不受影响
      this.addIssue('css-reset', 'info', '检测到CSS重置', '已应用防护措施');
      this.addFix('protectFromCSSReset');
    }
  }

  checkPerformanceIssues() {
    // 检查页面复杂度
    const elementCount = document.querySelectorAll('*').length;
    if (elementCount > 5000) {
      this.addIssue('performance', 'warning', '页面元素过多', '可能影响动画性能');
      this.addFix('enablePerformanceMode');
    }
    
    // 检查大量动画元素
    const animatedElements = document.querySelectorAll('[style*="animation"], .animated');
    if (animatedElements.length > 50) {
      this.addIssue('performance', 'warning', '页面动画过多', '可能导致性能问题');
      this.addFix('limitAnimations');
    }
    
    // 检查视频元素
    const videoElements = document.querySelectorAll('video');
    if (videoElements.length > 0) {
      this.addIssue('performance', 'info', '页面包含视频', '可能影响性能');
      this.addFix('avoidVideoAreas');
    }
  }

  addIssue(type, severity, message, description) {
    this.issues.push({
      type,
      severity,
      message,
      description,
      timestamp: Date.now()
    });
  }

  addFix(fixName) {
    if (!this.fixes.includes(fixName)) {
      this.fixes.push(fixName);
    }
  }

  async applyFixes() {
    const results = [];
    
    for (const fixName of this.fixes) {
      try {
        const result = await this.applyFix(fixName);
        results.push({ fix: fixName, success: result.success, message: result.message });
      } catch (error) {
        results.push({ fix: fixName, success: false, message: error.message });
      }
    }
    
    return results;
  }

  async applyFix(fixName) {
    switch (fixName) {
      case 'adjustZIndex':
        return this.fixZIndex();
      
      case 'handleFullscreen':
        return this.fixFullscreen();
      
      case 'respectCustomCursor':
        return this.fixCustomCursor();
      
      case 'handleInfiniteScroll':
        return this.fixInfiniteScroll();
      
      case 'protectFromCSSReset':
        return this.fixCSSReset();
      
      case 'enablePerformanceMode':
        return this.fixPerformanceMode();
      
      case 'polyfillRequestAnimationFrame':
        return this.fixRequestAnimationFrame();
      
      default:
        return { success: false, message: `未知的修复方法: ${fixName}` };
    }
  }

  fixZIndex() {
    const petElements = document.querySelectorAll('.pet-cursor');
    petElements.forEach(el => {
      el.style.zIndex = '2147483647';
    });
    return { success: true, message: '已调整z-index值' };
  }

  fixFullscreen() {
    // 监听全屏变化事件
    document.addEventListener('fullscreenchange', () => {
      const petElements = document.querySelectorAll('.pet-cursor');
      if (document.fullscreenElement) {
        // 进入全屏，隐藏宠物
        petElements.forEach(el => el.style.display = 'none');
      } else {
        // 退出全屏，显示宠物
        petElements.forEach(el => el.style.display = 'block');
      }
    });
    
    return { success: true, message: '已设置全屏处理' };
  }

  fixCustomCursor() {
    // 在有自定义光标的区域降低宠物透明度
    const customCursorElements = document.querySelectorAll('[style*="cursor:"]');
    customCursorElements.forEach(el => {
      el.addEventListener('mouseenter', () => {
        const petElements = document.querySelectorAll('.pet-cursor');
        petElements.forEach(pet => pet.style.opacity = '0.3');
      });
      
      el.addEventListener('mouseleave', () => {
        const petElements = document.querySelectorAll('.pet-cursor');
        petElements.forEach(pet => pet.style.opacity = '1');
      });
    });
    
    return { success: true, message: '已处理自定义光标冲突' };
  }

  fixInfiniteScroll() {
    // 在快速滚动时暂停动画
    let scrollTimeout;
    window.addEventListener('scroll', () => {
      const petElements = document.querySelectorAll('.pet-cursor');
      petElements.forEach(el => el.style.animationPlayState = 'paused');
      
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        petElements.forEach(el => el.style.animationPlayState = 'running');
      }, 150);
    });
    
    return { success: true, message: '已优化滚动性能' };
  }

  fixCSSReset() {
    // 添加保护性CSS
    const style = document.createElement('style');
    style.textContent = `
      .pet-cursor {
        all: initial !important;
        position: fixed !important;
        pointer-events: none !important;
        z-index: 2147483647 !important;
      }
    `;
    document.head.appendChild(style);
    
    return { success: true, message: '已添加CSS保护' };
  }

  fixPerformanceMode() {
    // 启用性能模式
    if (window.petCursor && window.petCursor.enablePerformanceMode) {
      window.petCursor.enablePerformanceMode();
      return { success: true, message: '已启用性能模式' };
    }
    
    return { success: false, message: '无法启用性能模式' };
  }

  fixRequestAnimationFrame() {
    // 添加requestAnimationFrame polyfill
    if (!window.requestAnimationFrame) {
      window.requestAnimationFrame = function(callback) {
        return setTimeout(callback, 16);
      };
      window.cancelAnimationFrame = function(id) {
        clearTimeout(id);
      };
      return { success: true, message: '已添加requestAnimationFrame polyfill' };
    }
    
    return { success: false, message: 'requestAnimationFrame已支持' };
  }

  generateReport() {
    return {
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      issues: this.issues,
      fixes: this.fixes,
      summary: {
        totalIssues: this.issues.length,
        errorCount: this.issues.filter(i => i.severity === 'error').length,
        warningCount: this.issues.filter(i => i.severity === 'warning').length,
        infoCount: this.issues.filter(i => i.severity === 'info').length
      }
    };
  }
}

// 导出
window.CompatibilityChecker = CompatibilityChecker;
