<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pet Cursor 插件测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
        }
        
        .test-section h2 {
            margin-top: 0;
            color: #FFE5E5;
        }
        
        .test-area {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
            border: 2px dashed rgba(255, 255, 255, 0.3);
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        
        .test-area:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.5);
        }
        
        .clickable-area {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            padding: 20px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            display: inline-block;
        }
        
        .clickable-area:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success {
            background: #4CAF50;
        }
        
        .status-warning {
            background: #FF9800;
        }
        
        .status-error {
            background: #F44336;
        }
        
        .instructions {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 15px;
            margin-top: 20px;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #E3F2FD;
        }
        
        .instructions ol {
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 10px;
        }
        
        .debug-info {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin-top: 20px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .button {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: transform 0.2s;
            margin: 5px;
        }
        
        .button:hover {
            transform: translateY(-2px);
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐾 Pet Cursor 插件测试</h1>
        
        <div class="test-section">
            <h2>📊 插件状态检测</h2>
            <div id="pluginStatus">
                <p><span class="status-indicator status-warning"></span>正在检测插件状态...</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🎯 鼠标跟随测试</h2>
            <div class="test-area" id="mouseFollowTest">
                <h3>在这个区域移动鼠标</h3>
                <p>如果插件正常工作，你应该能看到可爱的小动物跟随鼠标移动</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>👆 点击动画测试</h2>
            <div class="grid">
                <div class="clickable-area" onclick="testClick(this, 'cat')">
                    🐱 点击测试小猫咪动画
                </div>
                <div class="clickable-area" onclick="testClick(this, 'dog')">
                    🐶 点击测试小狗狗动画
                </div>
                <div class="clickable-area" onclick="testClick(this, 'rabbit')">
                    🐰 点击测试小兔子动画
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🎨 悬停动画测试</h2>
            <div class="test-area" id="hoverTest">
                <h3>将鼠标悬停在这里</h3>
                <p>观察小动物的悬停动画效果</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>⚙️ 控制面板测试</h2>
            <div>
                <button class="button" onclick="togglePlugin()">切换插件开关</button>
                <button class="button" onclick="changePetType()">切换动物类型</button>
                <button class="button" onclick="changeSize()">改变大小</button>
                <button class="button" onclick="testPerformance()">性能测试</button>
            </div>
        </div>
        
        <div class="instructions">
            <h3>📋 测试说明</h3>
            <ol>
                <li><strong>安装插件</strong>：确保Pet Cursor插件已经安装并启用</li>
                <li><strong>检查状态</strong>：页面顶部会显示插件的检测状态</li>
                <li><strong>移动鼠标</strong>：在测试区域移动鼠标，观察动物跟随效果</li>
                <li><strong>点击测试</strong>：点击不同的测试按钮，观察点击动画</li>
                <li><strong>悬停测试</strong>：将鼠标悬停在测试区域，观察悬停动画</li>
                <li><strong>功能测试</strong>：使用控制按钮测试各种功能</li>
            </ol>
            
            <h3>🔍 预期效果</h3>
            <ul>
                <li>✅ 小动物应该平滑地跟随鼠标移动</li>
                <li>✅ 点击时应该有放大或特殊动画效果</li>
                <li>✅ 悬停时应该有摇摆或好奇动画</li>
                <li>✅ 移动时应该有走路或飞行动画</li>
                <li>✅ 静止时应该有呼吸或待机动画</li>
            </ul>
        </div>
        
        <div class="debug-info" id="debugInfo">
            <strong>调试信息：</strong><br>
            <div id="debugLog">等待插件响应...</div>
        </div>
    </div>

    <script>
        let currentPetType = 'cat';
        let currentSize = 32;
        let pluginEnabled = true;
        
        // 检测插件状态
        function checkPluginStatus() {
            const statusDiv = document.getElementById('pluginStatus');
            const debugLog = document.getElementById('debugLog');
            
            // 检查是否有Pet Cursor元素
            const petElements = document.querySelectorAll('.pet-cursor');
            const hasContentScript = typeof window.petCursor !== 'undefined';
            
            let status = '';
            let statusClass = '';
            
            if (petElements.length > 0) {
                status = '插件已激活并正在运行';
                statusClass = 'status-success';
                debugLog.innerHTML += `✅ 检测到 ${petElements.length} 个宠物元素<br>`;
            } else if (hasContentScript) {
                status = '插件已加载但未显示宠物';
                statusClass = 'status-warning';
                debugLog.innerHTML += '⚠️ 内容脚本已加载但未创建宠物元素<br>';
            } else {
                status = '插件未检测到或未启用';
                statusClass = 'status-error';
                debugLog.innerHTML += '❌ 未检测到插件内容脚本<br>';
            }
            
            statusDiv.innerHTML = `<p><span class="status-indicator ${statusClass}"></span>${status}</p>`;
            
            // 检查扩展API
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                debugLog.innerHTML += '✅ Chrome扩展API可用<br>';
                
                // 尝试与background脚本通信
                chrome.runtime.sendMessage({type: 'PING'}, (response) => {
                    if (chrome.runtime.lastError) {
                        debugLog.innerHTML += '❌ 无法与background脚本通信<br>';
                    } else {
                        debugLog.innerHTML += '✅ 与background脚本通信正常<br>';
                    }
                });
            } else {
                debugLog.innerHTML += '❌ Chrome扩展API不可用<br>';
            }
        }
        
        // 测试点击功能
        function testClick(element, petType) {
            const debugLog = document.getElementById('debugLog');
            debugLog.innerHTML += `🖱️ 测试点击动画 (${petType})<br>`;
            
            // 添加视觉反馈
            element.style.transform = 'scale(0.95)';
            setTimeout(() => {
                element.style.transform = 'translateY(-5px)';
            }, 100);
        }
        
        // 切换插件
        function togglePlugin() {
            pluginEnabled = !pluginEnabled;
            const debugLog = document.getElementById('debugLog');
            debugLog.innerHTML += `🔄 切换插件状态: ${pluginEnabled ? '启用' : '禁用'}<br>`;
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                chrome.runtime.sendMessage({
                    type: 'TOGGLE_EXTENSION',
                    enabled: pluginEnabled
                });
            }
        }
        
        // 切换动物类型
        function changePetType() {
            const petTypes = ['cat', 'dog', 'rabbit', 'bird', 'panda'];
            const currentIndex = petTypes.indexOf(currentPetType);
            currentPetType = petTypes[(currentIndex + 1) % petTypes.length];
            
            const debugLog = document.getElementById('debugLog');
            debugLog.innerHTML += `🐾 切换动物类型: ${currentPetType}<br>`;
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                chrome.runtime.sendMessage({
                    type: 'SETTINGS_UPDATED',
                    settings: { petType: currentPetType }
                });
            }
        }
        
        // 改变大小
        function changeSize() {
            const sizes = [24, 32, 48, 64];
            const currentIndex = sizes.indexOf(currentSize);
            currentSize = sizes[(currentIndex + 1) % sizes.length];
            
            const debugLog = document.getElementById('debugLog');
            debugLog.innerHTML += `📏 改变大小: ${currentSize}px<br>`;
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                chrome.runtime.sendMessage({
                    type: 'SETTINGS_UPDATED',
                    settings: { size: currentSize }
                });
            }
        }
        
        // 性能测试
        function testPerformance() {
            const debugLog = document.getElementById('debugLog');
            debugLog.innerHTML += '⚡ 开始性能测试...<br>';
            
            // 模拟大量鼠标移动
            let moveCount = 0;
            const maxMoves = 100;
            const startTime = performance.now();
            
            const interval = setInterval(() => {
                // 模拟鼠标移动事件
                const event = new MouseEvent('mousemove', {
                    clientX: Math.random() * window.innerWidth,
                    clientY: Math.random() * window.innerHeight
                });
                document.dispatchEvent(event);
                
                moveCount++;
                if (moveCount >= maxMoves) {
                    clearInterval(interval);
                    const endTime = performance.now();
                    const duration = endTime - startTime;
                    debugLog.innerHTML += `✅ 性能测试完成: ${maxMoves}次移动耗时 ${duration.toFixed(2)}ms<br>`;
                }
            }, 10);
        }
        
        // 页面加载完成后开始检测
        window.addEventListener('load', () => {
            setTimeout(checkPluginStatus, 1000);
            
            // 定期检查插件状态
            setInterval(checkPluginStatus, 5000);
        });
        
        // 监听鼠标移动以提供反馈
        document.addEventListener('mousemove', (e) => {
            // 可以在这里添加鼠标移动的调试信息
        });
        
        // 监听来自插件的消息
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
                const debugLog = document.getElementById('debugLog');
                debugLog.innerHTML += `📨 收到消息: ${JSON.stringify(message)}<br>`;
                
                // 自动滚动到底部
                debugLog.scrollTop = debugLog.scrollHeight;
            });
        }
    </script>
</body>
</html>
