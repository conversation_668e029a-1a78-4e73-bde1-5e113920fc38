// Pet Cursor 雪碧图管理器
class SpriteManager {
  constructor() {
    this.sprites = new Map();
    this.loadedSprites = new Set();
    this.spriteConfigs = this.getDefaultConfigs();
    this.fallbackMode = true; // 使用CSS占位符
  }

  // 默认雪碧图配置
  getDefaultConfigs() {
    return {
      cat: {
        frameWidth: 32,
        frameHeight: 32,
        animations: {
          idle: { row: 0, frames: 4, fps: 8, loop: true },
          walk: { row: 1, frames: 6, fps: 12, loop: true },
          click: { row: 2, frames: 4, fps: 15, loop: false },
          hover: { row: 3, frames: 4, fps: 10, loop: true }
        },
        colors: {
          primary: '#FF6B6B',
          secondary: '#FFE5E5'
        }
      },
      dog: {
        frameWidth: 32,
        frameHeight: 32,
        animations: {
          idle: { row: 0, frames: 4, fps: 8, loop: true },
          walk: { row: 1, frames: 6, fps: 12, loop: true },
          click: { row: 2, frames: 4, fps: 15, loop: false },
          hover: { row: 3, frames: 4, fps: 10, loop: true }
        },
        colors: {
          primary: '#4ECDC4',
          secondary: '#E8F8F7'
        }
      },
      rabbit: {
        frameWidth: 32,
        frameHeight: 32,
        animations: {
          idle: { row: 0, frames: 4, fps: 8, loop: true },
          walk: { row: 1, frames: 6, fps: 12, loop: true },
          click: { row: 2, frames: 4, fps: 15, loop: false },
          hover: { row: 3, frames: 4, fps: 10, loop: true }
        },
        colors: {
          primary: '#45B7D1',
          secondary: '#E3F2FD'
        }
      },
      bird: {
        frameWidth: 32,
        frameHeight: 32,
        animations: {
          idle: { row: 0, frames: 4, fps: 8, loop: true },
          walk: { row: 1, frames: 6, fps: 12, loop: true },
          click: { row: 2, frames: 4, fps: 15, loop: false },
          hover: { row: 3, frames: 4, fps: 10, loop: true }
        },
        colors: {
          primary: '#96CEB4',
          secondary: '#F0F8F4'
        }
      },
      panda: {
        frameWidth: 32,
        frameHeight: 32,
        animations: {
          idle: { row: 0, frames: 4, fps: 8, loop: true },
          walk: { row: 1, frames: 6, fps: 12, loop: true },
          click: { row: 2, frames: 4, fps: 15, loop: false },
          hover: { row: 3, frames: 4, fps: 10, loop: true }
        },
        colors: {
          primary: '#FECA57',
          secondary: '#FFF8E1'
        }
      }
    };
  }

  // 异步加载雪碧图
  async loadSprite(petType) {
    if (this.loadedSprites.has(petType)) {
      return this.sprites.get(petType);
    }

    // 首先尝试PNG
    try {
      const pngPath = chrome.runtime.getURL(`assets/sprites/${petType}.png`);
      const img = new Image();

      const pngResult = await new Promise((resolve, reject) => {
        img.onload = () => {
          this.sprites.set(petType, { image: img, type: 'png' });
          this.loadedSprites.add(petType);
          this.fallbackMode = false;
          console.log(`✅ 成功加载 ${petType} 的PNG雪碧图`);
          resolve(img);
        };

        img.onerror = () => reject(new Error('PNG加载失败'));
        img.src = pngPath;
      });

      return pngResult;

    } catch (pngError) {
      console.warn(`PNG雪碧图加载失败: ${petType}, 尝试SVG...`);

      // 尝试SVG
      try {
        const svgPath = chrome.runtime.getURL(`assets/sprites/${petType}.svg`);
        const response = await fetch(svgPath);

        if (!response.ok) {
          throw new Error(`SVG加载失败: ${response.status}`);
        }

        const svgContent = await response.text();
        this.sprites.set(petType, { svgContent: svgContent, type: 'svg' });
        this.loadedSprites.add(petType);
        this.fallbackMode = false;
        console.log(`✅ 成功加载 ${petType} 的SVG雪碧图`);
        return { svgContent, type: 'svg' };

      } catch (svgError) {
        console.warn(`SVG雪碧图加载失败: ${petType}, 使用占位符模式`);
        this.createFallbackSprite(petType);
        return null;
      }
    }
  }

  // 创建CSS占位符
  createFallbackSprite(petType) {
    const config = this.spriteConfigs[petType];
    if (!config) return;

    // 创建占位符样式
    const styleId = `pet-cursor-fallback-${petType}`;
    let style = document.getElementById(styleId);
    
    if (!style) {
      style = document.createElement('style');
      style.id = styleId;
      document.head.appendChild(style);
    }

    style.textContent = `
      .pet-cursor-${petType} {
        width: ${config.frameWidth}px;
        height: ${config.frameHeight}px;
        background: linear-gradient(45deg, ${config.colors.primary}, ${config.colors.secondary});
        border-radius: 50%;
        position: relative;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      }

      .pet-cursor-${petType}::before {
        content: '';
        position: absolute;
        top: 20%;
        left: 20%;
        width: 25%;
        height: 25%;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 50%;
        box-shadow: 35% 0 0 rgba(255, 255, 255, 0.8);
      }

      .pet-cursor-${petType}::after {
        content: '';
        position: absolute;
        bottom: 20%;
        left: 50%;
        width: 30%;
        height: 15%;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 50%;
        transform: translateX(-50%);
      }

      .pet-cursor-${petType}.animate-idle {
        animation: pet-idle-${petType} 2s ease-in-out infinite;
      }

      .pet-cursor-${petType}.animate-walk {
        animation: pet-walk-${petType} 0.5s ease-in-out infinite;
      }

      .pet-cursor-${petType}.animate-click {
        animation: pet-click-${petType} 0.3s ease-in-out;
      }

      .pet-cursor-${petType}.animate-hover {
        animation: pet-hover-${petType} 1s ease-in-out infinite;
      }

      @keyframes pet-idle-${petType} {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
      }

      @keyframes pet-walk-${petType} {
        0%, 100% { transform: translateY(0) rotate(0deg); }
        25% { transform: translateY(-2px) rotate(-2deg); }
        75% { transform: translateY(-2px) rotate(2deg); }
      }

      @keyframes pet-click-${petType} {
        0% { transform: scale(1); }
        50% { transform: scale(1.2); }
        100% { transform: scale(1); }
      }

      @keyframes pet-hover-${petType} {
        0%, 100% { transform: rotate(0deg); }
        25% { transform: rotate(-5deg); }
        75% { transform: rotate(5deg); }
      }
    `;
  }

  // 获取雪碧图配置
  getConfig(petType) {
    return this.spriteConfigs[petType] || this.spriteConfigs.cat;
  }

  // 计算雪碧图背景位置
  getBackgroundPosition(petType, animationType, frame) {
    const config = this.getConfig(petType);
    const animation = config.animations[animationType];
    
    if (!animation) return '0 0';

    const x = frame * config.frameWidth;
    const y = animation.row * config.frameHeight;
    
    return `-${x}px -${y}px`;
  }

  // 创建宠物元素
  createElement(petType, size = 32) {
    const element = document.createElement('div');
    element.className = `pet-cursor pet-cursor-${petType}`;

    // 设置尺寸
    element.style.width = `${size}px`;
    element.style.height = `${size}px`;

    if (this.fallbackMode || !this.loadedSprites.has(petType)) {
      // 使用CSS占位符
      this.createFallbackSprite(petType);
    } else {
      // 使用真实雪碧图
      const sprite = this.sprites.get(petType);

      if (sprite.type === 'png') {
        // PNG雪碧图
        const spritePath = chrome.runtime.getURL(`assets/sprites/${petType}.png`);
        element.style.backgroundImage = `url(${spritePath})`;
        element.style.backgroundRepeat = 'no-repeat';
        element.style.backgroundSize = `${sprite.image.width}px ${sprite.image.height}px`;
      } else if (sprite.type === 'svg') {
        // SVG雪碧图 - 转换为data URL
        const svgDataUrl = `data:image/svg+xml;base64,${btoa(sprite.svgContent)}`;
        element.style.backgroundImage = `url(${svgDataUrl})`;
        element.style.backgroundRepeat = 'no-repeat';
        element.style.backgroundSize = `${size * 6}px ${size * 4}px`; // 6列4行
      }
    }

    return element;
  }

  // 预加载所有雪碧图
  async preloadAll() {
    const petTypes = Object.keys(this.spriteConfigs);
    const loadPromises = petTypes.map(petType => this.loadSprite(petType));
    
    try {
      await Promise.all(loadPromises);
      console.log('所有雪碧图预加载完成');
    } catch (error) {
      console.warn('部分雪碧图加载失败，将使用占位符', error);
    }
  }

  // 检查雪碧图是否可用
  isAvailable(petType) {
    return this.loadedSprites.has(petType) || this.fallbackMode;
  }

  // 获取所有可用的宠物类型
  getAvailablePets() {
    return Object.keys(this.spriteConfigs);
  }

  // 清理资源
  cleanup() {
    this.sprites.clear();
    this.loadedSprites.clear();
    
    // 移除占位符样式
    Object.keys(this.spriteConfigs).forEach(petType => {
      const style = document.getElementById(`pet-cursor-fallback-${petType}`);
      if (style) {
        style.remove();
      }
    });
  }
}

// 雪碧图动画播放器
class SpriteAnimator {
  constructor(element, petType, spriteManager) {
    this.element = element;
    this.petType = petType;
    this.spriteManager = spriteManager;
    this.config = spriteManager.getConfig(petType);

    this.currentAnimation = 'idle';
    this.currentFrame = 0;
    this.isPlaying = false;
    this.frameTimer = null;
    this.animationSpeed = 1.0;
    this.loop = true;

    this.init();
  }

  init() {
    // 设置元素基础样式
    this.setupElement();

    // 开始播放默认动画
    this.play('idle');
  }

  setupElement() {
    if (!this.element) return;

    const { frameWidth, frameHeight } = this.config;

    // 设置元素尺寸
    this.element.style.width = `${frameWidth}px`;
    this.element.style.height = `${frameHeight}px`;

    // 如果有真实雪碧图，设置背景
    if (this.spriteManager.isAvailable(this.petType) && !this.spriteManager.fallbackMode) {
      const spritePath = chrome.runtime.getURL(`assets/sprites/${this.petType}.png`);
      this.element.style.backgroundImage = `url(${spritePath})`;
      this.element.style.backgroundRepeat = 'no-repeat';

      // 计算雪碧图总尺寸
      const sprite = this.spriteManager.sprites.get(this.petType);
      if (sprite) {
        this.element.style.backgroundSize = `${sprite.width}px ${sprite.height}px`;
      }
    }
  }

  play(animationName, options = {}) {
    const animation = this.config.animations[animationName];
    if (!animation) {
      console.warn(`动画不存在: ${animationName}`);
      return;
    }

    // 停止当前动画
    this.stop();

    // 设置新动画参数
    this.currentAnimation = animationName;
    this.currentFrame = 0;
    this.isPlaying = true;
    this.loop = options.loop !== undefined ? options.loop : animation.loop;
    this.animationSpeed = options.speed || this.animationSpeed;

    // 开始播放
    this.startFrameLoop();

    // 立即显示第一帧
    this.updateFrame();
  }

  stop() {
    this.isPlaying = false;
    if (this.frameTimer) {
      clearInterval(this.frameTimer);
      this.frameTimer = null;
    }
  }

  pause() {
    this.isPlaying = false;
    if (this.frameTimer) {
      clearInterval(this.frameTimer);
      this.frameTimer = null;
    }
  }

  resume() {
    if (!this.isPlaying) {
      this.isPlaying = true;
      this.startFrameLoop();
    }
  }

  startFrameLoop() {
    if (this.frameTimer) return;

    const animation = this.config.animations[this.currentAnimation];
    const frameDelay = (1000 / animation.fps) / this.animationSpeed;

    this.frameTimer = setInterval(() => {
      this.nextFrame();
    }, frameDelay);
  }

  nextFrame() {
    if (!this.isPlaying) return;

    const animation = this.config.animations[this.currentAnimation];

    this.currentFrame++;

    // 检查是否到达最后一帧
    if (this.currentFrame >= animation.frames) {
      if (this.loop) {
        this.currentFrame = 0;
      } else {
        this.currentFrame = animation.frames - 1;
        this.stop();
        this.onAnimationComplete();
        return;
      }
    }

    this.updateFrame();
  }

  updateFrame() {
    if (!this.element) return;

    if (this.spriteManager.fallbackMode || !this.spriteManager.isAvailable(this.petType)) {
      // 使用CSS动画类
      this.updateCSSAnimation();
    } else {
      // 使用雪碧图
      this.updateSpriteFrame();
    }
  }

  updateSpriteFrame() {
    const { frameWidth, frameHeight } = this.config;
    const animation = this.config.animations[this.currentAnimation];

    const x = this.currentFrame * frameWidth;
    const y = animation.row * frameHeight;

    this.element.style.backgroundPosition = `-${x}px -${y}px`;
  }

  updateCSSAnimation() {
    // 移除所有动画类
    this.element.classList.remove('animate-idle', 'animate-walk', 'animate-click', 'animate-hover');

    // 添加当前动画类
    this.element.classList.add(`animate-${this.currentAnimation}`);
  }

  setSpeed(speed) {
    this.animationSpeed = Math.max(0.1, Math.min(3.0, speed));

    // 如果正在播放，重新开始以应用新速度
    if (this.isPlaying) {
      const currentAnim = this.currentAnimation;
      const currentLoop = this.loop;
      this.play(currentAnim, { loop: currentLoop, speed: this.animationSpeed });
    }
  }

  setSize(size) {
    if (!this.element) return;

    this.element.style.width = `${size}px`;
    this.element.style.height = `${size}px`;

    // 如果使用雪碧图，需要按比例缩放
    if (!this.spriteManager.fallbackMode && this.spriteManager.isAvailable(this.petType)) {
      const scale = size / this.config.frameWidth;
      const sprite = this.spriteManager.sprites.get(this.petType);

      if (sprite) {
        this.element.style.backgroundSize = `${sprite.width * scale}px ${sprite.height * scale}px`;
      }
    }
  }

  getCurrentAnimation() {
    return this.currentAnimation;
  }

  getCurrentFrame() {
    return this.currentFrame;
  }

  isAnimationPlaying() {
    return this.isPlaying;
  }

  onAnimationComplete() {
    // 动画完成回调，可以被重写
    this.element.dispatchEvent(new CustomEvent('animationComplete', {
      detail: {
        animation: this.currentAnimation,
        petType: this.petType
      }
    }));
  }

  cleanup() {
    this.stop();
    this.element = null;
    this.spriteManager = null;
  }
}

// 动画状态机
class AnimationStateMachine {
  constructor(animator) {
    this.animator = animator;
    this.currentState = 'idle';
    this.previousState = 'idle';
    this.stateTimeout = null;
    this.stateHistory = [];
    this.maxHistoryLength = 10;

    // 状态转换规则
    this.transitionRules = {
      idle: ['walk', 'click', 'hover'],
      walk: ['idle', 'click'],
      click: ['idle', 'walk'],
      hover: ['idle', 'walk']
    };

    // 状态优先级（数字越大优先级越高）
    this.statePriority = {
      idle: 1,
      hover: 2,
      walk: 3,
      click: 4
    };
  }

  setState(newState, options = {}) {
    // 检查状态转换是否合法
    if (!this.canTransitionTo(newState)) {
      console.warn(`无法从 ${this.currentState} 转换到 ${newState}`);
      return false;
    }

    // 检查优先级
    if (!options.force && this.statePriority[newState] < this.statePriority[this.currentState]) {
      console.log(`状态 ${newState} 优先级低于当前状态 ${this.currentState}，忽略`);
      return false;
    }

    // 保存状态历史
    this.addToHistory(this.currentState);

    // 更新状态
    this.previousState = this.currentState;
    this.currentState = newState;

    // 清除之前的超时
    if (this.stateTimeout) {
      clearTimeout(this.stateTimeout);
      this.stateTimeout = null;
    }

    // 播放动画
    this.animator.play(newState, options);

    // 设置自动返回
    if (options.duration) {
      this.stateTimeout = setTimeout(() => {
        this.returnToPreviousState();
      }, options.duration);
    }

    return true;
  }

  canTransitionTo(newState) {
    const allowedTransitions = this.transitionRules[this.currentState];
    return allowedTransitions && allowedTransitions.includes(newState);
  }

  returnToPreviousState() {
    if (this.previousState && this.previousState !== this.currentState) {
      this.setState(this.previousState, { force: true });
    } else {
      this.setState('idle', { force: true });
    }
  }

  addToHistory(state) {
    this.stateHistory.push({
      state: state,
      timestamp: Date.now()
    });

    // 限制历史记录长度
    if (this.stateHistory.length > this.maxHistoryLength) {
      this.stateHistory.shift();
    }
  }

  getStateHistory() {
    return [...this.stateHistory];
  }

  getCurrentState() {
    return this.currentState;
  }

  getPreviousState() {
    return this.previousState;
  }

  cleanup() {
    if (this.stateTimeout) {
      clearTimeout(this.stateTimeout);
    }
    this.animator = null;
  }
}

// 扩展SpriteManager类
SpriteManager.prototype.createAnimator = function(element, petType) {
  return new SpriteAnimator(element, petType, this);
};

SpriteManager.prototype.createStateMachine = function(animator) {
  return new AnimationStateMachine(animator);
};

// 导出类
window.SpriteAnimator = SpriteAnimator;
window.AnimationStateMachine = AnimationStateMachine;

// 导出单例
window.SpriteManager = window.SpriteManager || new SpriteManager();
