/* 基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
  overflow-x: hidden;
}

.popup-container {
  width: 320px;
  max-height: 600px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  overflow-y: auto;
}

/* 头部样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  border-radius: 12px 12px 0 0;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-icon {
  width: 24px;
  height: 24px;
}

.logo h1 {
  font-size: 18px;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 开关按钮 */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.3);
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .toggle-slider {
  background-color: #4CAF50;
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

/* 区域样式 */
.section {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.section:last-child {
  border-bottom: none;
}

.section h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
}

/* 宠物选择 */
.pet-selection {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.pet-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
}

.pet-option:hover {
  border-color: #ff9a9e;
  background: #fff;
  transform: translateY(-2px);
}

.pet-option.active {
  border-color: #ff9a9e;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: white;
}

.pet-preview {
  width: 32px;
  height: 32px;
  background-size: cover;
  background-position: center;
  border-radius: 4px;
  margin-bottom: 8px;
}

.pet-preview.cat { background-color: #ff6b6b; }
.pet-preview.dog { background-color: #4ecdc4; }
.pet-preview.rabbit { background-color: #45b7d1; }
.pet-preview.bird { background-color: #96ceb4; }
.pet-preview.panda { background-color: #feca57; }

.pet-option span {
  font-size: 12px;
  font-weight: 500;
}

/* 设置项 */
.setting-item {
  margin-bottom: 16px;
}

.setting-item label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #555;
}

.slider-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.slider-container input[type="range"] {
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: #e0e0e0;
  outline: none;
  -webkit-appearance: none;
}

.slider-container input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #ff9a9e;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider-value {
  font-size: 12px;
  font-weight: 600;
  color: #666;
  min-width: 40px;
  text-align: right;
}

/* 复选框样式 */
.checkbox-item label {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-bottom: 0;
}

.checkbox-item input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid #ddd;
  border-radius: 4px;
  margin-right: 10px;
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-item input[type="checkbox"]:checked + .checkmark {
  background: #ff9a9e;
  border-color: #ff9a9e;
}

.checkbox-item input[type="checkbox"]:checked + .checkmark:after {
  content: "✓";
  position: absolute;
  top: -2px;
  left: 2px;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* 底部按钮 */
.footer {
  padding: 16px 20px;
  display: flex;
  gap: 12px;
  background: #f8f9fa;
  border-radius: 0 0 12px 12px;
}

.btn {
  flex: 1;
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-secondary {
  background: #e9ecef;
  color: #6c757d;
}

.btn-secondary:hover {
  background: #dee2e6;
}

.btn-primary {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 154, 158, 0.4);
}

/* 预览区域 */
.preview-area {
  position: relative;
  height: 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin: 16px 20px;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-pet {
  width: 32px;
  height: 32px;
  background: #ff6b6b;
  border-radius: 4px;
  position: absolute;
  transition: all 0.1s ease;
}

.preview-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  text-align: center;
}

/* 滚动条样式 */
.popup-container::-webkit-scrollbar {
  width: 6px;
}

.popup-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.popup-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.popup-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 网站管理样式 */
.current-site {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.current-site span {
  flex: 1;
  font-size: 12px;
  color: #666;
  word-break: break-all;
}

.btn-small {
  padding: 4px 8px;
  font-size: 11px;
  min-width: 50px;
}

.disabled-sites-list {
  max-height: 120px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: #f8f9fa;
}

.disabled-site-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border-bottom: 1px solid #e9ecef;
  font-size: 12px;
}

.disabled-site-item:last-child {
  border-bottom: none;
}

.disabled-site-url {
  flex: 1;
  color: #666;
  word-break: break-all;
}

.remove-site-btn {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 3px;
  padding: 2px 6px;
  font-size: 10px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.remove-site-btn:hover {
  background: #c82333;
}

.empty-sites-message {
  padding: 20px;
  text-align: center;
  color: #999;
  font-size: 12px;
}

/* 性能模式指示器 */
.performance-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #28a745;
  margin-left: 5px;
}

.performance-indicator.warning {
  background: #ffc107;
}

.performance-indicator.error {
  background: #dc3545;
}
