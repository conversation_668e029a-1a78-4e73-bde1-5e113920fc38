<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pet Cursor 简单测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-area {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: center;
            min-height: 200px;
            border: 2px dashed rgba(255, 255, 255, 0.3);
        }
        
        .status {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
        }
        
        .button {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        
        .button:hover {
            transform: translateY(-2px);
        }
        
        .debug-info {
            background: rgba(0, 0, 0, 0.5);
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐾 Pet Cursor 简单测试</h1>
        
        <div class="status" id="status">
            正在检测插件状态...
        </div>
        
        <div class="test-area">
            <h2>在这里移动鼠标</h2>
            <p>如果插件正常工作，你应该能看到可爱的小动物跟随鼠标移动</p>
            <p>鼠标坐标: <span id="mousePos">0, 0</span></p>
        </div>
        
        <div>
            <button class="button" onclick="checkPlugin()">检查插件状态</button>
            <button class="button" onclick="forceCreate()">强制创建宠物</button>
            <button class="button" onclick="clearLog()">清空日志</button>
        </div>
        
        <div class="debug-info" id="debugLog">
            等待调试信息...
        </div>
    </div>

    <script>
        let debugLog = document.getElementById('debugLog');
        let status = document.getElementById('status');
        let mousePos = document.getElementById('mousePos');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.innerHTML += `[${timestamp}] ${message}<br>`;
            debugLog.scrollTop = debugLog.scrollHeight;
        }
        
        function updateStatus(message, isError = false) {
            status.textContent = message;
            status.style.background = isError ? 'rgba(255, 0, 0, 0.3)' : 'rgba(0, 255, 0, 0.3)';
        }
        
        function checkPlugin() {
            log('开始检查插件状态...');
            
            // 检查是否有Pet Cursor元素
            const petElements = document.querySelectorAll('.pet-cursor');
            log(`找到 ${petElements.length} 个宠物元素`);
            
            if (petElements.length > 0) {
                petElements.forEach((el, index) => {
                    const rect = el.getBoundingClientRect();
                    log(`宠物 ${index + 1}: 位置(${rect.left}, ${rect.top}), 大小(${rect.width}x${rect.height}), 可见性: ${el.style.visibility || 'visible'}`);
                });
                updateStatus('✅ 插件已激活，找到宠物元素');
            } else {
                updateStatus('❌ 未找到宠物元素', true);
            }
            
            // 检查全局变量
            if (typeof window.petCursor !== 'undefined') {
                log('✅ 找到全局 petCursor 对象');
            } else {
                log('❌ 未找到全局 petCursor 对象');
            }
            
            // 检查Chrome扩展API
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                log('✅ Chrome扩展API可用');
            } else {
                log('❌ Chrome扩展API不可用');
            }
        }
        
        function forceCreate() {
            log('尝试强制创建宠物元素...');
            
            // 创建一个简单的测试元素
            const testPet = document.createElement('div');
            testPet.className = 'pet-cursor pet-cursor-cat test-pet';
            testPet.style.cssText = `
                position: fixed;
                width: 32px;
                height: 32px;
                background: linear-gradient(45deg, #FF6B6B, #FFE5E5);
                border-radius: 50%;
                pointer-events: none;
                z-index: 2147483647;
                left: 100px;
                top: 100px;
                opacity: 0.9;
                transition: all 0.3s ease;
            `;
            
            // 添加眼睛
            testPet.innerHTML = `
                <div style="position: absolute; top: 20%; left: 20%; width: 25%; height: 25%; background: white; border-radius: 50%;"></div>
                <div style="position: absolute; top: 20%; right: 20%; width: 25%; height: 25%; background: white; border-radius: 50%;"></div>
                <div style="position: absolute; top: 22%; left: 22%; width: 15%; height: 15%; background: black; border-radius: 50%;"></div>
                <div style="position: absolute; top: 22%; right: 22%; width: 15%; height: 15%; background: black; border-radius: 50%;"></div>
            `;
            
            document.body.appendChild(testPet);
            log('✅ 测试宠物元素已创建');
            
            // 添加简单的鼠标跟随
            let mouseX = 0, mouseY = 0;
            let currentX = 100, currentY = 100;
            
            document.addEventListener('mousemove', (e) => {
                mouseX = e.clientX;
                mouseY = e.clientY;
            });
            
            function animate() {
                const dx = mouseX - currentX;
                const dy = mouseY - currentY;
                
                currentX += dx * 0.1;
                currentY += dy * 0.1;
                
                testPet.style.left = `${currentX - 16}px`;
                testPet.style.top = `${currentY - 16}px`;
                
                requestAnimationFrame(animate);
            }
            
            animate();
            log('✅ 测试动画已启动');
        }
        
        function clearLog() {
            debugLog.innerHTML = '日志已清空<br>';
        }
        
        // 监听鼠标移动
        document.addEventListener('mousemove', (e) => {
            mousePos.textContent = `${e.clientX}, ${e.clientY}`;
        });
        
        // 页面加载完成后检查
        window.addEventListener('load', () => {
            log('页面加载完成');
            setTimeout(() => {
                checkPlugin();
            }, 1000);
        });
        
        // 监听控制台日志
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        console.log = function(...args) {
            if (args[0] && args[0].includes && args[0].includes('Pet Cursor')) {
                log(`LOG: ${args.join(' ')}`);
            }
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            if (args[0] && args[0].includes && args[0].includes('Pet Cursor')) {
                log(`ERROR: ${args.join(' ')}`);
            }
            originalError.apply(console, args);
        };
        
        console.warn = function(...args) {
            if (args[0] && args[0].includes && args[0].includes('Pet Cursor')) {
                log(`WARN: ${args.join(' ')}`);
            }
            originalWarn.apply(console, args);
        };
        
        log('测试页面初始化完成');
    </script>
</body>
</html>
