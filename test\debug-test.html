<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pet Cursor 调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
            min-height: 100vh;
        }
        
        .debug-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            background: white;
            border: 1px solid #ccc;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            z-index: 1000;
            font-size: 12px;
        }
        
        .test-pet {
            position: fixed;
            width: 32px;
            height: 32px;
            background: linear-gradient(45deg, #FF6B6B, #FFE5E5);
            border-radius: 50%;
            pointer-events: none;
            z-index: 999999;
            transition: none;
            transform: translateZ(0);
        }
        
        .content {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            border-radius: 8px;
            margin-top: 50px;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="debug-panel">
        <h3>🐾 Pet Cursor 调试面板</h3>
        <div>
            <strong>插件状态:</strong> <span id="pluginStatus">检测中...</span>
        </div>
        <div>
            <strong>鼠标位置:</strong> <span id="mousePos">0, 0</span>
        </div>
        <div>
            <strong>宠物元素:</strong> <span id="petCount">0</span>
        </div>
        <div style="margin-top: 10px;">
            <button onclick="checkPlugin()">检查插件</button>
            <button onclick="createTestPet()">创建测试宠物</button>
            <button onclick="clearAll()">清空所有</button>
        </div>
        <div class="log" id="logArea">等待日志...</div>
    </div>
    
    <div class="content">
        <h1>Pet Cursor 调试测试页面</h1>
        <p>这是一个用于调试Pet Cursor插件的测试页面。</p>
        
        <h2>测试说明</h2>
        <ul>
            <li>移动鼠标，观察右上角的鼠标位置显示</li>
            <li>检查是否有宠物元素跟随鼠标移动</li>
            <li>使用调试面板中的按钮进行测试</li>
            <li>查看浏览器控制台的详细日志信息</li>
        </ul>
        
        <h2>预期行为</h2>
        <ul>
            <li>页面加载后应该自动创建一个可爱的小动物</li>
            <li>小动物应该跟随鼠标移动</li>
            <li>移动鼠标时应该有平滑的跟随动画</li>
            <li>点击时应该有特殊的动画效果</li>
        </ul>
        
        <div style="height: 500px; background: linear-gradient(45deg, #e3f2fd, #f3e5f5); border-radius: 8px; margin: 20px 0; display: flex; align-items: center; justify-content: center; font-size: 18px; color: #666;">
            在这个区域移动鼠标测试跟随效果
        </div>
    </div>

    <script>
        let logArea = document.getElementById('logArea');
        let pluginStatus = document.getElementById('pluginStatus');
        let mousePos = document.getElementById('mousePos');
        let petCount = document.getElementById('petCount');
        
        let testPet = null;
        let mouseX = 0, mouseY = 0;
        let currentX = 100, currentY = 100;
        
        function log(message) {
            const time = new Date().toLocaleTimeString();
            logArea.innerHTML += `[${time}] ${message}<br>`;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(`Pet Cursor Debug: ${message}`);
        }
        
        function updateCounts() {
            const pets = document.querySelectorAll('.pet-cursor, .test-pet');
            petCount.textContent = pets.length;
        }
        
        function checkPlugin() {
            log('开始检查插件状态...');

            // 检查宠物元素
            const petElements = document.querySelectorAll('.pet-cursor');
            log(`找到 ${petElements.length} 个官方宠物元素`);

            // 详细检查每个宠物元素
            petElements.forEach((pet, index) => {
                const rect = pet.getBoundingClientRect();
                const styles = window.getComputedStyle(pet);

                log(`宠物 ${index + 1} 详情:`);
                log(`  位置: left=${pet.style.left}, top=${pet.style.top}`);
                log(`  计算位置: x=${rect.left}, y=${rect.top}, w=${rect.width}, h=${rect.height}`);
                log(`  可见性: display=${styles.display}, visibility=${styles.visibility}, opacity=${styles.opacity}`);
                log(`  z-index: ${styles.zIndex}`);
                log(`  类名: ${pet.className}`);

                // 强制显示宠物元素
                pet.style.display = 'block';
                pet.style.visibility = 'visible';
                pet.style.opacity = '1';
                pet.style.backgroundColor = '#FF6B6B';
                pet.style.border = '2px solid #FF0000';
                pet.style.left = '200px';
                pet.style.top = '200px';

                log(`  已强制设置宠物 ${index + 1} 为可见状态`);
            });

            // 检查全局对象
            if (typeof window.petCursor !== 'undefined') {
                log('✅ 找到全局 petCursor 对象');
                pluginStatus.textContent = '已激活';
                pluginStatus.style.color = 'green';
            } else {
                log('❌ 未找到全局 petCursor 对象');
                pluginStatus.textContent = '未激活';
                pluginStatus.style.color = 'red';
            }

            // 检查Chrome API
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                log('✅ Chrome扩展API可用');

                // 尝试获取扩展信息
                try {
                    const manifest = chrome.runtime.getManifest();
                    log(`✅ 扩展版本: ${manifest.version}`);
                } catch (e) {
                    log(`❌ 无法获取扩展信息: ${e.message}`);
                }
            } else {
                log('❌ Chrome扩展API不可用（这在直接打开HTML文件时是正常的）');
            }

            updateCounts();
        }
        
        function createTestPet() {
            log('创建测试宠物...');
            
            // 清除现有测试宠物
            if (testPet) {
                testPet.remove();
            }
            
            // 创建新的测试宠物
            testPet = document.createElement('div');
            testPet.className = 'test-pet';
            testPet.style.left = '100px';
            testPet.style.top = '100px';
            
            // 添加眼睛
            testPet.innerHTML = `
                <div style="position: absolute; top: 6px; left: 6px; width: 8px; height: 8px; background: white; border-radius: 50%;"></div>
                <div style="position: absolute; top: 6px; right: 6px; width: 8px; height: 8px; background: white; border-radius: 50%;"></div>
                <div style="position: absolute; top: 8px; left: 8px; width: 4px; height: 4px; background: black; border-radius: 50%;"></div>
                <div style="position: absolute; top: 8px; right: 8px; width: 4px; height: 4px; background: black; border-radius: 50%;"></div>
            `;
            
            document.body.appendChild(testPet);
            log('✅ 测试宠物已创建');
            
            // 启动动画
            function animate() {
                if (!testPet) return;
                
                const dx = mouseX - currentX;
                const dy = mouseY - currentY;
                
                currentX += dx * 0.1;
                currentY += dy * 0.1;
                
                testPet.style.left = `${currentX - 16}px`;
                testPet.style.top = `${currentY - 16}px`;
                
                requestAnimationFrame(animate);
            }
            
            animate();
            log('✅ 测试动画已启动');
            updateCounts();
        }
        
        function clearAll() {
            log('清空所有测试元素...');
            
            // 清除测试宠物
            if (testPet) {
                testPet.remove();
                testPet = null;
            }
            
            // 清除所有测试元素
            document.querySelectorAll('.test-pet').forEach(el => el.remove());
            
            updateCounts();
            log('✅ 已清空');
        }
        
        // 监听鼠标移动
        document.addEventListener('mousemove', (e) => {
            mouseX = e.clientX;
            mouseY = e.clientY;
            mousePos.textContent = `${mouseX}, ${mouseY}`;
        });
        
        // 页面加载完成后自动检查
        window.addEventListener('load', () => {
            log('页面加载完成');
            setTimeout(() => {
                checkPlugin();
                // 如果没有检测到插件，自动创建测试宠物
                if (document.querySelectorAll('.pet-cursor').length === 0) {
                    log('未检测到插件宠物，创建测试宠物...');
                    createTestPet();
                }
            }, 2000);
        });
        
        // 定期更新状态
        setInterval(() => {
            updateCounts();
        }, 1000);
        
        log('调试页面初始化完成');
    </script>
</body>
</html>
