# Pet Cursor 图标资源

## 📋 图标规格要求

### 基础规格
- **格式**: PNG（支持透明背景）
- **尺寸**: 16x16, 32x32, 48x48, 128x128 像素
- **背景**: 透明或白色
- **风格**: 与插件主题一致的可爱风格

### 图标设计指南

#### 主图标设计元素
1. **主体**: 可爱的小动物轮廓（建议使用小猫咪）
2. **鼠标指针**: 小型鼠标指针图标
3. **动态元素**: 简单的跟随轨迹或星星装饰
4. **配色**: 使用品牌色彩 #FF6B6B (珊瑚红)

#### 不同尺寸的设计要点

##### 16x16px (工具栏图标)
- 极简设计，只保留最核心元素
- 高对比度，确保在小尺寸下清晰可见
- 建议只显示动物头部轮廓

##### 32x32px (扩展管理页面)
- 可以包含更多细节
- 动物形象更完整
- 可以添加简单的装饰元素

##### 48x48px (扩展详情页面)
- 完整的动物形象
- 鼠标指针和跟随效果
- 丰富的色彩和细节

##### 128x128px (Chrome Web Store)
- 最详细的版本
- 完整的场景设计
- 可以包含文字或标语
- 高质量的渐变和阴影效果

## 🎨 临时SVG图标生成器

由于暂时没有专业设计的图标，我们可以使用SVG生成临时图标：

### 16x16 图标
```svg
<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
  <circle cx="8" cy="8" r="6" fill="#FF6B6B" opacity="0.8"/>
  <circle cx="6" cy="6" r="1" fill="white"/>
  <circle cx="10" cy="6" r="1" fill="white"/>
  <path d="M5 10 Q8 12 11 10" stroke="white" stroke-width="1" fill="none"/>
  <polygon points="12,2 14,2 14,4 12,4" fill="#333" opacity="0.7"/>
</svg>
```

### 32x32 图标
```svg
<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <circle cx="16" cy="16" r="12" fill="#FF6B6B" opacity="0.9"/>
  <circle cx="12" cy="12" r="2" fill="white"/>
  <circle cx="20" cy="12" r="2" fill="white"/>
  <path d="M10 20 Q16 24 22 20" stroke="white" stroke-width="2" fill="none"/>
  <polygon points="24,4 28,4 28,8 24,8" fill="#333" opacity="0.8"/>
  <path d="M26 6 L30 10" stroke="#333" stroke-width="1"/>
</svg>
```

### 48x48 图标
```svg
<svg width="48" height="48" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="petGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6B6B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFE5E5;stop-opacity:1" />
    </linearGradient>
  </defs>
  <circle cx="24" cy="24" r="18" fill="url(#petGradient)"/>
  <circle cx="18" cy="18" r="3" fill="white"/>
  <circle cx="30" cy="18" r="3" fill="white"/>
  <circle cx="18" cy="18" r="1" fill="#333"/>
  <circle cx="30" cy="18" r="1" fill="#333"/>
  <path d="M15 30 Q24 36 33 30" stroke="white" stroke-width="3" fill="none"/>
  <polygon points="36,6 42,6 42,12 36,12" fill="#333" opacity="0.8"/>
  <path d="M39 9 L45 15" stroke="#333" stroke-width="2"/>
  <circle cx="42" cy="12" r="1" fill="#FF6B6B" opacity="0.6"/>
</svg>
```

### 128x128 图标
```svg
<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="mainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6B6B;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FF9A9E;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FECFEF;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- 主体 -->
  <circle cx="64" cy="64" r="48" fill="url(#mainGradient)" filter="url(#shadow)"/>
  
  <!-- 耳朵 -->
  <ellipse cx="45" cy="25" rx="8" ry="12" fill="url(#mainGradient)"/>
  <ellipse cx="83" cy="25" rx="8" ry="12" fill="url(#mainGradient)"/>
  
  <!-- 眼睛 -->
  <circle cx="50" cy="50" r="8" fill="white"/>
  <circle cx="78" cy="50" r="8" fill="white"/>
  <circle cx="50" cy="50" r="4" fill="#333"/>
  <circle cx="78" cy="50" r="4" fill="#333"/>
  <circle cx="52" cy="48" r="2" fill="white" opacity="0.8"/>
  <circle cx="80" cy="48" r="2" fill="white" opacity="0.8"/>
  
  <!-- 鼻子 -->
  <ellipse cx="64" cy="65" rx="3" ry="2" fill="#FF6B6B"/>
  
  <!-- 嘴巴 -->
  <path d="M55 75 Q64 85 73 75" stroke="white" stroke-width="4" fill="none"/>
  
  <!-- 鼠标指针 -->
  <polygon points="90,15 105,15 105,30 90,30" fill="#333" opacity="0.9"/>
  <path d="M97.5 22.5 L110 35" stroke="#333" stroke-width="3"/>
  
  <!-- 跟随轨迹 -->
  <circle cx="85" cy="25" r="2" fill="#FF6B6B" opacity="0.6"/>
  <circle cx="80" cy="30" r="1.5" fill="#FF6B6B" opacity="0.4"/>
  <circle cx="75" cy="35" r="1" fill="#FF6B6B" opacity="0.2"/>
  
  <!-- 装饰星星 -->
  <polygon points="20,20 22,16 24,20 28,20 25,23 26,27 22,25 18,27 19,23 16,20" fill="#FECA57" opacity="0.7"/>
  <polygon points="100,100 101,98 102,100 104,100 102,101 103,103 101,102 99,103 100,101 98,100" fill="#FECA57" opacity="0.5"/>
</svg>
```

## 🔧 图标生成脚本

创建一个简单的HTML文件来生成和预览图标：

```html
<!DOCTYPE html>
<html>
<head>
    <title>Pet Cursor 图标生成器</title>
    <style>
        .icon-preview {
            display: inline-block;
            margin: 10px;
            text-align: center;
        }
        .icon-size {
            border: 1px solid #ddd;
            margin: 5px;
            background: #f5f5f5;
        }
    </style>
</head>
<body>
    <h1>Pet Cursor 图标预览</h1>
    
    <div class="icon-preview">
        <h3>16x16</h3>
        <div class="icon-size">
            <!-- 16x16 SVG 这里 -->
        </div>
    </div>
    
    <div class="icon-preview">
        <h3>32x32</h3>
        <div class="icon-size">
            <!-- 32x32 SVG 这里 -->
        </div>
    </div>
    
    <div class="icon-preview">
        <h3>48x48</h3>
        <div class="icon-size">
            <!-- 48x48 SVG 这里 -->
        </div>
    </div>
    
    <div class="icon-preview">
        <h3>128x128</h3>
        <div class="icon-size">
            <!-- 128x128 SVG 这里 -->
        </div>
    </div>
</body>
</html>
```

## 📝 制作清单

### 必需图标 (MVP)
- [ ] icon16.png - 工具栏图标
- [ ] icon32.png - 扩展管理页面图标
- [ ] icon48.png - 扩展详情页面图标
- [ ] icon128.png - Chrome Web Store 图标

### 可选图标
- [ ] disabled-icon16.png - 禁用状态图标
- [ ] disabled-icon32.png - 禁用状态图标

### 制作工具
- **在线SVG转PNG**: https://convertio.co/svg-png/
- **图标优化**: https://tinypng.com/
- **SVG编辑**: https://boxy-svg.com/

### 质量检查
- [ ] 在不同背景下的可见性测试
- [ ] 不同浏览器的显示效果
- [ ] 高DPI屏幕显示效果
- [ ] 文件大小优化（每个图标 < 5KB）
