# Pet Cursor 雪碧图资源规格

## 📋 雪碧图规格要求

### 基础规格
- **格式**: PNG（支持透明背景）
- **单帧尺寸**: 32x32px（默认）
- **背景**: 完全透明
- **风格**: 可爱卡通风格，像素艺术或矢量风格
- **颜色**: 鲜艳但不刺眼的配色方案

### 动物类型和动画状态

#### 1. 小猫咪 (cat.png)
**雪碧图布局**: 4行 × 最多6列
- **第0行 - 静止状态**: 4帧呼吸动画
  - 帧1: 正常状态
  - 帧2: 轻微膨胀
  - 帧3: 最大膨胀
  - 帧4: 回到正常
- **第1行 - 移动状态**: 6帧跑步动画
  - 帧1-6: 跑步循环动画
- **第2行 - 点击状态**: 4帧互动动画
  - 帧1: 正常
  - 帧2: 惊讶表情
  - 帧3: 开心表情
  - 帧4: 回到正常
- **第3行 - 悬停状态**: 4帧好奇动画
  - 帧1-4: 左右张望动画

#### 2. 小狗狗 (dog.png)
**雪碧图布局**: 4行 × 最多6列
- **第0行 - 静止状态**: 4帧尾巴摆动
- **第1行 - 移动状态**: 6帧奔跑动画
- **第2行 - 点击状态**: 4帧摇尾巴动画
- **第3行 - 悬停状态**: 4帧抬头动画

#### 3. 小兔子 (rabbit.png)
**雪碧图布局**: 4行 × 最多6列
- **第0行 - 静止状态**: 4帧耳朵抖动
- **第1行 - 移动状态**: 6帧跳跃动画
- **第2行 - 点击状态**: 4帧吃胡萝卜动画
- **第3行 - 悬停状态**: 4帧耳朵竖起动画

#### 4. 小鸟 (bird.png)
**雪碧图布局**: 4行 × 最多6列
- **第0行 - 静止状态**: 4帧翅膀轻拍
- **第1行 - 移动状态**: 6帧飞行动画
- **第2行 - 点击状态**: 4帧啄食动画
- **第3行 - 悬停状态**: 4帧转头动画

#### 5. 小熊猫 (panda.png)
**雪碧图布局**: 4行 × 最多6列
- **第0行 - 静止状态**: 4帧坐着摇摆
- **第1行 - 移动状态**: 6帧滚动前进
- **第2行 - 点击状态**: 4帧吃竹子动画
- **第3行 - 悬停状态**: 4帧挥手动画

## 🎨 设计指南

### 色彩方案
```
小猫咪: 主色 #FF6B6B (珊瑚红), 辅色 #FFE5E5 (浅粉)
小狗狗: 主色 #4ECDC4 (青绿色), 辅色 #E8F8F7 (浅青)
小兔子: 主色 #45B7D1 (天蓝色), 辅色 #E3F2FD (浅蓝)
小鸟:   主色 #96CEB4 (薄荷绿), 辅色 #F0F8F4 (浅绿)
小熊猫: 主色 #FECA57 (金黄色), 辅色 #FFF8E1 (浅黄)
```

### 动画原则
1. **流畅性**: 每个动画循环应该无缝衔接
2. **可爱性**: 动作要夸张但不失可爱
3. **识别性**: 每种动物要有明显的特征
4. **一致性**: 所有动物的动画风格要统一

## 📁 文件命名规范

```
assets/sprites/
├── cat.png          # 小猫咪雪碧图
├── dog.png          # 小狗狗雪碧图
├── rabbit.png       # 小兔子雪碧图
├── bird.png         # 小鸟雪碧图
├── panda.png        # 小熊猫雪碧图
├── cat_large.png    # 大尺寸版本 (64x64)
├── dog_large.png
├── rabbit_large.png
├── bird_large.png
└── panda_large.png
```

## 🔧 技术要求

### 优化要求
- 文件大小控制在50KB以内（每个雪碧图）
- 使用PNG-8格式（如果颜色数量允许）
- 启用PNG压缩优化
- 确保透明背景正确

### 测试要求
- 在不同背景色上测试显示效果
- 确保动画帧之间过渡自然
- 验证在不同尺寸下的清晰度

## 🎯 制作工具推荐

### 专业工具
- **Aseprite**: 专业像素艺术工具
- **Photoshop**: 传统图像编辑
- **GIMP**: 免费开源选择
- **Procreate**: iPad上的绘画工具

### 在线工具
- **Piskel**: 在线像素艺术编辑器
- **Canva**: 简单的图形设计工具
- **Figma**: 矢量图形设计

## 📐 雪碧图坐标计算

### JavaScript中的使用示例
```javascript
// 雪碧图配置
const spriteConfig = {
  frameWidth: 32,
  frameHeight: 32,
  animations: {
    idle: { row: 0, frames: 4, fps: 8 },
    walk: { row: 1, frames: 6, fps: 12 },
    click: { row: 2, frames: 4, fps: 15 },
    hover: { row: 3, frames: 4, fps: 10 }
  }
};

// 计算背景位置
function getBackgroundPosition(row, frame) {
  const x = frame * spriteConfig.frameWidth;
  const y = row * spriteConfig.frameHeight;
  return `-${x}px -${y}px`;
}
```

## 🚀 临时占位符

在正式美术资源完成前，可以使用CSS生成的占位符：

```css
.pet-placeholder {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  position: relative;
  overflow: hidden;
}

.pet-placeholder.cat {
  background: linear-gradient(45deg, #FF6B6B, #FFE5E5);
}

.pet-placeholder.dog {
  background: linear-gradient(45deg, #4ECDC4, #E8F8F7);
}

/* 添加简单的动画效果 */
.pet-placeholder::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60%;
  height: 60%;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); }
  50% { transform: translate(-50%, -50%) scale(1.1); }
}
```

## 📝 制作清单

### 必需资源 (MVP)
- [ ] cat.png - 小猫咪基础雪碧图
- [ ] dog.png - 小狗狗基础雪碧图
- [ ] rabbit.png - 小兔子基础雪碧图

### 扩展资源
- [ ] bird.png - 小鸟雪碧图
- [ ] panda.png - 小熊猫雪碧图
- [ ] 大尺寸版本 (64x64px)
- [ ] 小尺寸版本 (24x24px)

### 质量检查
- [ ] 动画流畅性测试
- [ ] 不同背景兼容性测试
- [ ] 文件大小优化
- [ ] 浏览器兼容性测试
