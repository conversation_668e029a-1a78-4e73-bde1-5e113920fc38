// 临时雪碧图创建脚本
// 这个脚本会创建基础的SVG雪碧图，然后可以转换为PNG

const fs = require('fs');
const path = require('path');

// 动物配置
const petConfigs = {
    cat: {
        name: '小猫咪',
        colors: ['#FF6B6B', '#FFE5E5'],
        features: 'cat'
    },
    dog: {
        name: '小狗狗',
        colors: ['#4ECDC4', '#E8F8F7'],
        features: 'dog'
    },
    rabbit: {
        name: '小兔子',
        colors: ['#45B7D1', '#E3F2FD'],
        features: 'rabbit'
    },
    bird: {
        name: '小鸟',
        colors: ['#96CEB4', '#F0F8F4'],
        features: 'bird'
    },
    panda: {
        name: '小熊猫',
        colors: ['#FECA57', '#FFF8E1'],
        features: 'panda'
    }
};

// 创建SVG雪碧图
function createSVGSprite(petType, config, frameSize = 32) {
    const spriteWidth = frameSize * 6; // 6列
    const spriteHeight = frameSize * 4; // 4行
    
    let svg = `<svg width="${spriteWidth}" height="${spriteHeight}" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <linearGradient id="grad-${petType}" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:${config.colors[0]};stop-opacity:1" />
            <stop offset="100%" style="stop-color:${config.colors[1]};stop-opacity:1" />
        </linearGradient>
    </defs>`;
    
    // 动画帧数配置
    const frameCounts = [4, 6, 4, 4]; // idle, walk, click, hover
    
    for (let row = 0; row < 4; row++) {
        for (let col = 0; col < frameCounts[row]; col++) {
            const x = col * frameSize;
            const y = row * frameSize;
            
            svg += createFrame(petType, config, x, y, frameSize, row, col);
        }
    }
    
    svg += '</svg>';
    return svg;
}

// 创建单个动画帧
function createFrame(petType, config, x, y, size, row, col) {
    const centerX = x + size / 2;
    const centerY = y + size / 2;
    const radius = size * 0.35;
    
    // 根据动画状态调整
    let scaleX = 1;
    let scaleY = 1;
    let rotation = 0;
    
    // 根据行和列调整动画效果
    switch (row) {
        case 0: // idle - 呼吸效果
            const idleScale = 1 + Math.sin(col * Math.PI / 2) * 0.05;
            scaleX = scaleY = idleScale;
            break;
        case 1: // walk - 移动效果
            rotation = Math.sin(col * Math.PI / 3) * 5;
            scaleY = 1 + Math.sin(col * Math.PI / 3) * 0.1;
            break;
        case 2: // click - 点击效果
            const clickScale = 1 + (col === 1 ? 0.2 : col === 2 ? 0.3 : 0);
            scaleX = scaleY = clickScale;
            break;
        case 3: // hover - 悬停效果
            rotation = Math.sin(col * Math.PI / 2) * 10;
            break;
    }
    
    let frame = `<g transform="translate(${centerX},${centerY}) scale(${scaleX},${scaleY}) rotate(${rotation})">`;
    
    // 主体
    frame += `<circle cx="0" cy="0" r="${radius}" fill="url(#grad-${petType})" opacity="0.9"/>`;
    
    // 添加动物特征
    frame += getAnimalFeatures(petType, size);
    
    // 眼睛
    frame += `<circle cx="${-radius * 0.3}" cy="${-radius * 0.2}" r="${radius * 0.15}" fill="white" opacity="0.9"/>`;
    frame += `<circle cx="${radius * 0.3}" cy="${-radius * 0.2}" r="${radius * 0.15}" fill="white" opacity="0.9"/>`;
    frame += `<circle cx="${-radius * 0.3}" cy="${-radius * 0.2}" r="${radius * 0.08}" fill="black"/>`;
    frame += `<circle cx="${radius * 0.3}" cy="${-radius * 0.2}" r="${radius * 0.08}" fill="black"/>`;
    
    // 嘴巴
    frame += `<ellipse cx="0" cy="${radius * 0.2}" rx="${radius * 0.2}" ry="${radius * 0.1}" fill="rgba(0,0,0,0.6)"/>`;
    
    frame += '</g>';
    
    return frame;
}

// 获取动物特征
function getAnimalFeatures(petType, size) {
    const radius = size * 0.35;
    
    switch (petType) {
        case 'cat':
            return `
                <polygon points="${-radius * 0.4},${-radius * 0.8} ${-radius * 0.2},${-radius * 0.5} ${-radius * 0.1},${-radius * 0.8}" fill="${petConfigs.cat.colors[0]}"/>
                <polygon points="${radius * 0.4},${-radius * 0.8} ${radius * 0.2},${-radius * 0.5} ${radius * 0.1},${-radius * 0.8}" fill="${petConfigs.cat.colors[0]}"/>
            `;
        case 'dog':
            return `
                <ellipse cx="${-radius * 0.4}" cy="${-radius * 0.3}" rx="${radius * 0.2}" ry="${radius * 0.4}" fill="${petConfigs.dog.colors[0]}" transform="rotate(-20)"/>
                <ellipse cx="${radius * 0.4}" cy="${-radius * 0.3}" rx="${radius * 0.2}" ry="${radius * 0.4}" fill="${petConfigs.dog.colors[0]}" transform="rotate(20)"/>
            `;
        case 'rabbit':
            return `
                <ellipse cx="${-radius * 0.2}" cy="${-radius * 0.8}" rx="${radius * 0.1}" ry="${radius * 0.5}" fill="${petConfigs.rabbit.colors[0]}"/>
                <ellipse cx="${radius * 0.2}" cy="${-radius * 0.8}" rx="${radius * 0.1}" ry="${radius * 0.5}" fill="${petConfigs.rabbit.colors[0]}"/>
            `;
        case 'bird':
            return `
                <ellipse cx="${-radius * 0.7}" cy="0" rx="${radius * 0.3}" ry="${radius * 0.15}" fill="${petConfigs.bird.colors[0]}" transform="rotate(-20)"/>
                <ellipse cx="${radius * 0.7}" cy="0" rx="${radius * 0.3}" ry="${radius * 0.15}" fill="${petConfigs.bird.colors[0]}" transform="rotate(20)"/>
            `;
        case 'panda':
            return `
                <circle cx="${-radius * 0.4}" cy="${-radius * 0.6}" r="${radius * 0.2}" fill="black"/>
                <circle cx="${radius * 0.4}" cy="${-radius * 0.6}" r="${radius * 0.2}" fill="black"/>
                <circle cx="${-radius * 0.3}" cy="${-radius * 0.2}" r="${radius * 0.2}" fill="black" opacity="0.6"/>
                <circle cx="${radius * 0.3}" cy="${-radius * 0.2}" r="${radius * 0.2}" fill="black" opacity="0.6"/>
            `;
        default:
            return '';
    }
}

// 创建所有雪碧图
function createAllSprites() {
    const spritesDir = path.join(__dirname, '..', 'assets', 'sprites');
    
    // 确保目录存在
    if (!fs.existsSync(spritesDir)) {
        fs.mkdirSync(spritesDir, { recursive: true });
    }
    
    // 为每种动物创建SVG雪碧图
    Object.keys(petConfigs).forEach(petType => {
        const config = petConfigs[petType];
        const svg = createSVGSprite(petType, config, 32);
        
        // 保存SVG文件
        const svgPath = path.join(spritesDir, `${petType}.svg`);
        fs.writeFileSync(svgPath, svg);
        
        console.log(`✅ 创建了 ${config.name} 的SVG雪碧图: ${svgPath}`);
    });
    
    console.log('\n🎉 所有临时雪碧图创建完成！');
    console.log('📝 接下来的步骤：');
    console.log('1. 打开 tools/sprite-generator.html 生成PNG文件');
    console.log('2. 或者使用在线SVG转PNG工具转换SVG文件');
    console.log('3. 将PNG文件放入 assets/sprites/ 目录');
    console.log('4. 重新加载浏览器插件');
}

// 如果直接运行此脚本
if (require.main === module) {
    createAllSprites();
}

module.exports = { createAllSprites, createSVGSprite };
