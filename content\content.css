/* Pet Cursor Content Script 样式 */

/* 基础宠物光标样式 */
.pet-cursor {
  position: fixed !important;
  pointer-events: none !important;
  user-select: none !important;
  z-index: 2147483647 !important; /* 最大z-index值 */
  
  /* 硬件加速 */
  transform: translateZ(0) !important;
  will-change: transform !important;
  
  /* 防止被其他样式影响 */
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  outline: none !important;
  box-sizing: border-box !important;
  
  /* 基础显示属性 */
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  
  /* 防止文字选择和拖拽 */
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  -webkit-user-drag: none !important;
  -khtml-user-drag: none !important;
  -moz-user-drag: none !important;
  -o-user-drag: none !important;
  user-drag: none !important;
  
  /* 确保不会影响页面布局 */
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
}

/* 雪碧图样式 */
.pet-cursor[style*="background-image"] {
  background-repeat: no-repeat !important;
  background-position: 0 0 !important;
  image-rendering: pixelated !important; /* 保持像素艺术的清晰度 */
  image-rendering: -moz-crisp-edges !important;
  image-rendering: crisp-edges !important;
}

/* 占位符样式基类 */
.pet-cursor:not([style*="background-image"]) {
  border-radius: 50% !important;
  position: relative !important;
  overflow: hidden !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

/* 占位符眼睛效果 */
.pet-cursor:not([style*="background-image"])::before {
  content: '' !important;
  position: absolute !important;
  top: 20% !important;
  left: 20% !important;
  width: 25% !important;
  height: 25% !important;
  background: rgba(255, 255, 255, 0.9) !important;
  border-radius: 50% !important;
  box-shadow: 35% 0 0 rgba(255, 255, 255, 0.9) !important;
}

/* 占位符嘴巴效果 */
.pet-cursor:not([style*="background-image"])::after {
  content: '' !important;
  position: absolute !important;
  bottom: 20% !important;
  left: 50% !important;
  width: 30% !important;
  height: 15% !important;
  background: rgba(0, 0, 0, 0.6) !important;
  border-radius: 50% !important;
  transform: translateX(-50%) !important;
}

/* 小猫咪占位符 */
.pet-cursor-cat:not([style*="background-image"]) {
  background: linear-gradient(45deg, #FF6B6B, #FFE5E5) !important;
}

/* 小狗狗占位符 */
.pet-cursor-dog:not([style*="background-image"]) {
  background: linear-gradient(45deg, #4ECDC4, #E8F8F7) !important;
}

/* 小兔子占位符 */
.pet-cursor-rabbit:not([style*="background-image"]) {
  background: linear-gradient(45deg, #45B7D1, #E3F2FD) !important;
}

/* 小鸟占位符 */
.pet-cursor-bird:not([style*="background-image"]) {
  background: linear-gradient(45deg, #96CEB4, #F0F8F4) !important;
}

/* 小熊猫占位符 */
.pet-cursor-panda:not([style*="background-image"]) {
  background: linear-gradient(45deg, #FECA57, #FFF8E1) !important;
}

/* 动画状态类 */

/* 静止状态动画 */
.pet-cursor.animate-idle {
  animation: pet-cursor-idle 2s ease-in-out infinite !important;
}

@keyframes pet-cursor-idle {
  0%, 100% { 
    transform: translateZ(0) scale(1) !important; 
  }
  50% { 
    transform: translateZ(0) scale(1.05) !important; 
  }
}

/* 移动状态动画 */
.pet-cursor.animate-walk {
  animation: pet-cursor-walk 0.5s ease-in-out infinite !important;
}

@keyframes pet-cursor-walk {
  0%, 100% { 
    transform: translateZ(0) translateY(0) rotate(0deg) !important; 
  }
  25% { 
    transform: translateZ(0) translateY(-2px) rotate(-2deg) !important; 
  }
  75% { 
    transform: translateZ(0) translateY(-2px) rotate(2deg) !important; 
  }
}

/* 点击状态动画 */
.pet-cursor.animate-click {
  animation: pet-cursor-click 0.3s ease-in-out !important;
}

@keyframes pet-cursor-click {
  0% { 
    transform: translateZ(0) scale(1) !important; 
  }
  50% { 
    transform: translateZ(0) scale(1.3) !important; 
  }
  100% { 
    transform: translateZ(0) scale(1) !important; 
  }
}

/* 悬停状态动画 */
.pet-cursor.animate-hover {
  animation: pet-cursor-hover 1s ease-in-out infinite !important;
}

@keyframes pet-cursor-hover {
  0%, 100% { 
    transform: translateZ(0) rotate(0deg) !important; 
  }
  25% { 
    transform: translateZ(0) rotate(-8deg) !important; 
  }
  75% { 
    transform: translateZ(0) rotate(8deg) !important; 
  }
}

/* 不同尺寸的动画调整 */
.pet-cursor[style*="width: 24px"] {
  /* 小尺寸动画幅度减小 */
}

.pet-cursor[style*="width: 24px"].animate-walk {
  animation: pet-cursor-walk-small 0.5s ease-in-out infinite !important;
}

@keyframes pet-cursor-walk-small {
  0%, 100% { 
    transform: translateZ(0) translateY(0) rotate(0deg) !important; 
  }
  25% { 
    transform: translateZ(0) translateY(-1px) rotate(-1deg) !important; 
  }
  75% { 
    transform: translateZ(0) translateY(-1px) rotate(1deg) !important; 
  }
}

.pet-cursor[style*="width: 48px"] {
  /* 大尺寸动画幅度增大 */
}

.pet-cursor[style*="width: 48px"].animate-walk {
  animation: pet-cursor-walk-large 0.5s ease-in-out infinite !important;
}

@keyframes pet-cursor-walk-large {
  0%, 100% { 
    transform: translateZ(0) translateY(0) rotate(0deg) !important; 
  }
  25% { 
    transform: translateZ(0) translateY(-3px) rotate(-3deg) !important; 
  }
  75% { 
    transform: translateZ(0) translateY(-3px) rotate(3deg) !important; 
  }
}

/* 性能优化 */
.pet-cursor * {
  pointer-events: none !important;
  user-select: none !important;
}

/* 确保在所有情况下都不会被隐藏 */
.pet-cursor {
  visibility: visible !important;
  display: block !important;
  opacity: inherit !important;
}

/* 防止与页面元素冲突 */
.pet-cursor {
  mix-blend-mode: normal !important;
  isolation: isolate !important;
}

/* 高对比度模式适配 */
@media (prefers-contrast: high) {
  .pet-cursor:not([style*="background-image"]) {
    border: 1px solid currentColor !important;
  }
}

/* 减少动画模式适配 */
@media (prefers-reduced-motion: reduce) {
  .pet-cursor {
    animation: none !important;
  }
  
  .pet-cursor.animate-idle {
    animation: pet-cursor-idle-reduced 4s ease-in-out infinite !important;
  }
  
  @keyframes pet-cursor-idle-reduced {
    0%, 100% { 
      transform: translateZ(0) scale(1) !important; 
    }
    50% { 
      transform: translateZ(0) scale(1.02) !important; 
    }
  }
}

/* 打印时隐藏 */
@media print {
  .pet-cursor {
    display: none !important;
  }
}

/* 确保在iframe中也能正常显示 */
html.pet-cursor-active {
  overflow: visible !important;
}

/* 防止影响页面滚动 */
body:has(.pet-cursor) {
  /* 不添加任何可能影响布局的样式 */
}
