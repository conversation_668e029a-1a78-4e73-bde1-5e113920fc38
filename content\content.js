// Pet Cursor Content Script - 核心鼠标跟随功能
class PetCursor {
  constructor() {
    this.isEnabled = true;
    this.settings = {
      enabled: true,
      petType: 'cat',
      speed: 1.0,
      opacity: 100,
      size: 32,
      sensitivity: 0.1,
      clickAnimation: true,
      hoverAnimation: true
    };
    
    // 鼠标跟随相关
    this.mouseX = 0;
    this.mouseY = 0;
    this.currentX = 0;
    this.currentY = 0;
    this.targetX = 0;
    this.targetY = 0;
    
    // 动画相关
    this.petElement = null;
    this.animationId = null;
    this.isAnimating = false;
    
    // 性能优化
    this.lastUpdateTime = 0;
    this.updateThreshold = 16; // 约60fps
    
    // 状态管理
    this.currentState = 'idle';
    this.stateTimeout = null;
    this.mouseVelocity = { x: 0, y: 0 };
    this.lastMousePos = { x: 0, y: 0 };

    // 性能监控和兼容性
    this.performanceMonitor = null;
    this.compatibilityChecker = null;
    this.performanceMode = false;

    this.init();
  }

  async init() {
    try {
      // 加载设置
      await this.loadSettings();
      
      // 检查是否应该在当前网站运行
      if (!this.shouldRunOnCurrentSite()) {
        return;
      }
      
      // 加载雪碧图管理器
      await this.loadSpriteManager();

      // 初始化性能监控和兼容性检查
      await this.initPerformanceAndCompatibility();

      // 创建宠物元素
      this.createPetElement();

      // 绑定事件
      this.bindEvents();

      // 开始动画循环
      this.startAnimation();

      console.log('Pet Cursor 已启动');
    } catch (error) {
      console.error('Pet Cursor 初始化失败:', error);
    }
  }

  async loadSettings() {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage({ type: 'GET_SETTINGS' }, (response) => {
        if (response) {
          this.settings = { ...this.settings, ...response };
          this.isEnabled = this.settings.enabled;
        }
        resolve();
      });
    });
  }

  shouldRunOnCurrentSite() {
    // 检查是否在禁用网站列表中
    const hostname = window.location.hostname;
    const disabledSites = this.settings.disabledSites || [];
    
    if (disabledSites.includes(hostname)) {
      return false;
    }
    
    // 检查特殊页面
    const specialPages = [
      'chrome://',
      'chrome-extension://',
      'moz-extension://',
      'edge://',
      'about:'
    ];
    
    return !specialPages.some(prefix => window.location.href.startsWith(prefix));
  }

  async loadSpriteManager() {
    // 动态加载雪碧图管理器
    if (!window.SpriteManager) {
      const script = document.createElement('script');
      script.src = chrome.runtime.getURL('utils/sprite-manager.js');
      document.head.appendChild(script);
      
      // 等待加载完成
      await new Promise((resolve) => {
        script.onload = resolve;
        setTimeout(resolve, 1000); // 超时保护
      });
    }
    
    // 预加载当前宠物的雪碧图
    if (window.SpriteManager) {
      await window.SpriteManager.loadSprite(this.settings.petType);
    }
  }

  async initPerformanceAndCompatibility() {
    try {
      // 加载性能监控器
      await this.loadPerformanceMonitor();

      // 加载兼容性检查器
      await this.loadCompatibilityChecker();

      // 运行兼容性检查
      if (this.compatibilityChecker) {
        const result = await this.compatibilityChecker.checkCompatibility();

        if (!result.compatible) {
          console.warn('Pet Cursor: 检测到兼容性问题', result.issues);

          // 自动应用修复
          await this.compatibilityChecker.applyFixes();
        }
      }

      // 启动性能监控
      if (this.performanceMonitor) {
        this.performanceMonitor.setCallback('onPerformanceIssue', (issues) => {
          console.warn('Pet Cursor: 性能问题', issues);

          // 自动优化
          if (this.performanceMonitor.autoOptimize()) {
            this.enablePerformanceMode();
          }
        });

        this.performanceMonitor.start();
      }

    } catch (error) {
      console.warn('Pet Cursor: 性能监控和兼容性检查初始化失败', error);
    }
  }

  async loadPerformanceMonitor() {
    if (!window.PerformanceMonitor) {
      return new Promise((resolve) => {
        const script = document.createElement('script');
        script.src = chrome.runtime.getURL('utils/performance-monitor.js');
        document.head.appendChild(script);

        script.onload = () => {
          if (window.PerformanceMonitor) {
            this.performanceMonitor = new window.PerformanceMonitor();
          }
          resolve();
        };

        script.onerror = () => resolve();
        setTimeout(resolve, 2000);
      });
    } else {
      this.performanceMonitor = new window.PerformanceMonitor();
    }
  }

  async loadCompatibilityChecker() {
    if (!window.CompatibilityChecker) {
      return new Promise((resolve) => {
        const script = document.createElement('script');
        script.src = chrome.runtime.getURL('utils/compatibility-checker.js');
        document.head.appendChild(script);

        script.onload = () => {
          if (window.CompatibilityChecker) {
            this.compatibilityChecker = new window.CompatibilityChecker();
          }
          resolve();
        };

        script.onerror = () => resolve();
        setTimeout(resolve, 2000);
      });
    } else {
      this.compatibilityChecker = new window.CompatibilityChecker();
    }
  }

  createPetElement() {
    // 移除已存在的元素
    this.removePetElement();
    
    // 使用雪碧图管理器创建元素
    if (window.SpriteManager) {
      this.petElement = window.SpriteManager.createElement(
        this.settings.petType, 
        this.settings.size
      );
    } else {
      // 降级方案：创建简单的占位符
      this.petElement = document.createElement('div');
      this.petElement.className = `pet-cursor pet-cursor-${this.settings.petType}`;
      this.petElement.style.width = `${this.settings.size}px`;
      this.petElement.style.height = `${this.settings.size}px`;
      this.petElement.style.backgroundColor = this.getPetColor(this.settings.petType);
      this.petElement.style.borderRadius = '50%';
    }
    
    // 设置基础样式
    this.petElement.style.position = 'fixed';
    this.petElement.style.pointerEvents = 'none';
    this.petElement.style.zIndex = '2147483647'; // 最大z-index
    this.petElement.style.opacity = this.settings.opacity / 100;
    this.petElement.style.transition = 'none';
    this.petElement.style.transform = 'translateZ(0)'; // 硬件加速
    this.petElement.style.willChange = 'transform';
    
    // 初始位置（屏幕外）
    this.petElement.style.left = '-100px';
    this.petElement.style.top = '-100px';
    
    // 添加到页面
    document.body.appendChild(this.petElement);
    
    // 设置初始状态
    this.setState('idle');
  }

  removePetElement() {
    if (this.petElement && this.petElement.parentNode) {
      this.petElement.parentNode.removeChild(this.petElement);
      this.petElement = null;
    }
  }

  getPetColor(petType) {
    const colors = {
      cat: '#FF6B6B',
      dog: '#4ECDC4',
      rabbit: '#45B7D1',
      bird: '#96CEB4',
      panda: '#FECA57'
    };
    return colors[petType] || colors.cat;
  }

  bindEvents() {
    // 鼠标移动事件
    document.addEventListener('mousemove', this.handleMouseMove.bind(this), { passive: true });
    
    // 鼠标点击事件
    if (this.settings.clickAnimation) {
      document.addEventListener('click', this.handleMouseClick.bind(this), { passive: true });
    }
    
    // 鼠标悬停事件
    if (this.settings.hoverAnimation) {
      document.addEventListener('mouseover', this.handleMouseHover.bind(this), { passive: true });
    }
    
    // 页面可见性变化
    document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
    
    // 窗口大小变化
    window.addEventListener('resize', this.handleResize.bind(this), { passive: true });
    
    // 监听来自background的消息
    chrome.runtime.onMessage.addListener(this.handleMessage.bind(this));
  }

  handleMouseMove(e) {
    if (!this.isEnabled || !this.petElement) return;
    
    // 更新鼠标位置
    this.mouseX = e.clientX;
    this.mouseY = e.clientY;
    
    // 计算鼠标速度
    this.calculateMouseVelocity();
    
    // 更新动画状态
    this.updateAnimationState();
  }

  calculateMouseVelocity() {
    this.mouseVelocity.x = this.mouseX - this.lastMousePos.x;
    this.mouseVelocity.y = this.mouseY - this.lastMousePos.y;
    this.lastMousePos = { x: this.mouseX, y: this.mouseY };
  }

  updateAnimationState() {
    const speed = Math.sqrt(
      this.mouseVelocity.x ** 2 + this.mouseVelocity.y ** 2
    );
    
    if (speed > 5) {
      this.setState('walk');
    } else if (speed < 1 && this.currentState === 'walk') {
      this.setState('idle');
    }
  }

  handleMouseClick(e) {
    if (!this.isEnabled || !this.settings.clickAnimation) return;
    
    this.setState('click');
    
    // 点击动画播放完后回到之前的状态
    if (this.stateTimeout) clearTimeout(this.stateTimeout);
    this.stateTimeout = setTimeout(() => {
      this.setState('idle');
    }, 500);
  }

  handleMouseHover(e) {
    if (!this.isEnabled || !this.settings.hoverAnimation) return;
    
    // 只在idle状态下触发hover动画
    if (this.currentState === 'idle') {
      this.setState('hover');
      
      if (this.stateTimeout) clearTimeout(this.stateTimeout);
      this.stateTimeout = setTimeout(() => {
        this.setState('idle');
      }, 1000);
    }
  }

  handleVisibilityChange() {
    if (document.hidden) {
      this.pauseAnimation();
    } else {
      this.resumeAnimation();
    }
  }

  handleResize() {
    // 窗口大小变化时，确保宠物位置正确
    if (this.petElement) {
      this.updatePosition();
    }
  }

  handleMessage(message, sender, sendResponse) {
    switch (message.type) {
      case 'SETTINGS_UPDATED':
        this.updateSettings(message.settings);
        break;
      case 'TOGGLE_EXTENSION':
        this.toggleExtension(message.enabled);
        break;
      case 'ENABLE_PERFORMANCE_MODE':
        this.enablePerformanceMode();
        break;
      case 'DISABLE_PERFORMANCE_MODE':
        this.disablePerformanceMode();
        break;
      default:
        break;
    }
  }

  setState(newState) {
    if (this.currentState === newState) return;
    
    this.currentState = newState;
    
    if (this.petElement) {
      // 移除之前的动画类
      this.petElement.classList.remove('animate-idle', 'animate-walk', 'animate-click', 'animate-hover');
      
      // 添加新的动画类
      this.petElement.classList.add(`animate-${newState}`);
    }
  }

  startAnimation() {
    if (this.isAnimating) return;
    
    this.isAnimating = true;
    this.animate();
  }

  animate() {
    if (!this.isAnimating || !this.isEnabled || !this.petElement) return;
    
    const now = performance.now();
    
    // 性能优化：限制更新频率
    if (now - this.lastUpdateTime < this.updateThreshold) {
      this.animationId = requestAnimationFrame(() => this.animate());
      return;
    }
    
    this.lastUpdateTime = now;
    
    // 计算目标位置（鼠标位置偏移）
    this.targetX = this.mouseX - this.settings.size / 2;
    this.targetY = this.mouseY - this.settings.size / 2;
    
    // 缓动算法
    const easing = this.settings.sensitivity;
    const deltaX = this.targetX - this.currentX;
    const deltaY = this.targetY - this.currentY;
    
    this.currentX += deltaX * easing;
    this.currentY += deltaY * easing;
    
    // 更新位置
    this.updatePosition();
    
    // 继续动画循环
    this.animationId = requestAnimationFrame(() => this.animate());
  }

  updatePosition() {
    if (!this.petElement) return;
    
    // 限制在视窗内
    const maxX = window.innerWidth - this.settings.size;
    const maxY = window.innerHeight - this.settings.size;
    
    const clampedX = Math.max(0, Math.min(this.currentX, maxX));
    const clampedY = Math.max(0, Math.min(this.currentY, maxY));
    
    // 使用transform提高性能
    this.petElement.style.transform = `translate(${clampedX}px, ${clampedY}px) translateZ(0)`;
  }

  pauseAnimation() {
    this.isAnimating = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
  }

  resumeAnimation() {
    if (!this.isAnimating && this.isEnabled) {
      this.startAnimation();
    }
  }

  updateSettings(newSettings) {
    const oldPetType = this.settings.petType;
    this.settings = { ...this.settings, ...newSettings };
    this.isEnabled = this.settings.enabled;
    
    if (!this.isEnabled) {
      this.hide();
      return;
    }
    
    // 如果宠物类型改变，重新创建元素
    if (oldPetType !== this.settings.petType) {
      this.createPetElement();
    } else if (this.petElement) {
      // 更新现有元素的样式
      this.petElement.style.width = `${this.settings.size}px`;
      this.petElement.style.height = `${this.settings.size}px`;
      this.petElement.style.opacity = this.settings.opacity / 100;
    }
    
    this.show();
  }

  toggleExtension(enabled) {
    this.isEnabled = enabled;
    
    if (enabled) {
      this.show();
      this.resumeAnimation();
    } else {
      this.hide();
      this.pauseAnimation();
    }
  }

  show() {
    if (this.petElement) {
      this.petElement.style.display = 'block';
    }
  }

  hide() {
    if (this.petElement) {
      this.petElement.style.display = 'none';
    }
  }

  enablePerformanceMode() {
    if (this.performanceMode) return;

    this.performanceMode = true;
    console.log('Pet Cursor: 启用性能模式');

    // 降低更新频率
    this.updateThreshold = 33; // 约30fps

    // 简化动画
    if (this.petElement) {
      this.petElement.classList.add('performance-mode');
    }

    // 禁用某些动画效果
    this.settings.clickAnimation = false;
    this.settings.hoverAnimation = false;

    // 减少动画复杂度
    if (this.animator) {
      this.animator.setSpeed(0.5);
    }

    // 添加性能模式样式
    this.addPerformanceModeStyles();
  }

  disablePerformanceMode() {
    if (!this.performanceMode) return;

    this.performanceMode = false;
    console.log('Pet Cursor: 禁用性能模式');

    // 恢复正常更新频率
    this.updateThreshold = 16; // 约60fps

    // 移除性能模式类
    if (this.petElement) {
      this.petElement.classList.remove('performance-mode');
    }

    // 恢复动画设置
    this.settings.clickAnimation = true;
    this.settings.hoverAnimation = true;

    // 恢复动画速度
    if (this.animator) {
      this.animator.setSpeed(this.settings.speed);
    }

    // 移除性能模式样式
    this.removePerformanceModeStyles();
  }

  addPerformanceModeStyles() {
    if (document.getElementById('pet-cursor-performance-styles')) return;

    const style = document.createElement('style');
    style.id = 'pet-cursor-performance-styles';
    style.textContent = `
      .pet-cursor.performance-mode {
        animation-duration: 4s !important;
        transition: none !important;
      }

      .pet-cursor.performance-mode.animate-walk {
        animation: pet-cursor-walk-simple 1s ease-in-out infinite !important;
      }

      @keyframes pet-cursor-walk-simple {
        0%, 100% { transform: translateZ(0) scale(1) !important; }
        50% { transform: translateZ(0) scale(1.02) !important; }
      }

      .pet-cursor.performance-mode.animate-click,
      .pet-cursor.performance-mode.animate-hover {
        animation: none !important;
      }
    `;

    document.head.appendChild(style);
  }

  removePerformanceModeStyles() {
    const style = document.getElementById('pet-cursor-performance-styles');
    if (style) {
      style.remove();
    }
  }

  cleanup() {
    this.pauseAnimation();
    this.removePetElement();
    
    // 清理事件监听器
    document.removeEventListener('mousemove', this.handleMouseMove);
    document.removeEventListener('click', this.handleMouseClick);
    document.removeEventListener('mouseover', this.handleMouseHover);
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    window.removeEventListener('resize', this.handleResize);
    
    // 清理定时器
    if (this.stateTimeout) {
      clearTimeout(this.stateTimeout);
    }
    
    // 清理雪碧图管理器
    if (window.SpriteManager) {
      window.SpriteManager.cleanup();
    }

    // 清理性能监控器
    if (this.performanceMonitor) {
      this.performanceMonitor.stop();
      this.performanceMonitor = null;
    }

    // 清理兼容性检查器
    this.compatibilityChecker = null;

    // 移除性能模式样式
    this.removePerformanceModeStyles();
  }
}

// 初始化Pet Cursor
let petCursor = null;

// 等待DOM加载完成
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initPetCursor);
} else {
  initPetCursor();
}

function initPetCursor() {
  // 避免重复初始化
  if (petCursor) {
    petCursor.cleanup();
  }
  
  petCursor = new PetCursor();
}

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
  if (petCursor) {
    petCursor.cleanup();
  }
});
