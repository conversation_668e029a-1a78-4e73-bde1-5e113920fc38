# 🐾 Pet Cursor 安装和测试指南

## 📦 安装步骤

### 1. 准备插件文件
确保你的项目目录结构如下：
```
网页鼠标跟随/
├── manifest.json
├── assets/
│   ├── icons/
│   └── sprites/
├── background/
├── content/
├── popup/
├── utils/
└── tools/
```

### 2. 生成雪碧图文件

#### 方法一：使用雪碧图生成器（推荐）
1. 打开 `tools/sprite-generator.html`
2. 选择动物类型和设置
3. 点击"生成雪碧图"
4. 点击"下载PNG"保存文件
5. 将下载的PNG文件放入 `assets/sprites/` 目录

#### 方法二：使用现有SVG文件
1. 项目已经包含了基础的SVG雪碧图文件
2. 可以直接使用，或者转换为PNG格式以获得更好的性能

### 3. 安装浏览器插件

#### Chrome/Edge 安装步骤：
1. 打开浏览器，进入扩展管理页面：
   - Chrome: `chrome://extensions/`
   - Edge: `edge://extensions/`

2. 启用"开发者模式"（右上角开关）

3. 点击"加载已解压的扩展程序"

4. 选择项目根目录（包含 manifest.json 的文件夹）

5. 插件安装完成后，应该能在扩展栏看到 Pet Cursor 图标

## 🧪 测试插件功能

### 1. 基础功能测试
1. 打开 `test/plugin-test.html` 测试页面
2. 检查页面顶部的插件状态指示器
3. 在测试区域移动鼠标，观察是否有小动物跟随

### 2. 动画效果测试
- **静止动画**：鼠标停止移动时的呼吸效果
- **移动动画**：鼠标移动时的跑步/飞行效果
- **点击动画**：点击时的放大/互动效果
- **悬停动画**：鼠标悬停时的摇摆/好奇效果

### 3. 设置面板测试
1. 点击浏览器工具栏中的 Pet Cursor 图标
2. 测试各种设置选项：
   - 动物类型切换
   - 大小调整
   - 透明度调整
   - 速度调整

## 🔧 故障排除

### 插件未显示
1. **检查安装**：确认插件已正确安装并启用
2. **检查权限**：确认插件有访问当前网站的权限
3. **检查控制台**：按F12打开开发者工具，查看控制台错误信息
4. **重新加载**：尝试重新加载插件或刷新页面

### 动物不跟随鼠标
1. **检查雪碧图**：确认 `assets/sprites/` 目录下有对应的图片文件
2. **检查CSS**：确认 `content/content.css` 文件完整
3. **检查脚本**：确认 `content/content.js` 和 `utils/sprite-manager.js` 文件完整

### 动画效果异常
1. **性能问题**：如果动画卡顿，尝试启用性能模式
2. **兼容性问题**：某些网站可能有CSS冲突，插件会自动尝试修复
3. **浏览器兼容性**：确保使用支持的浏览器版本

### 常见错误信息
- `雪碧图加载失败`: 检查 `assets/sprites/` 目录下的文件
- `内容脚本注入失败`: 检查网站是否允许扩展运行
- `权限被拒绝`: 检查插件权限设置

## 📊 性能优化

### 自动优化功能
插件包含自动性能监控和优化功能：
- 检测页面性能影响
- 自动降低动画复杂度
- 智能调整更新频率

### 手动优化设置
1. **降低尺寸**：使用较小的动物尺寸
2. **减少透明度**：适当降低透明度可提高性能
3. **禁用复杂动画**：在设置中关闭点击和悬停动画
4. **启用性能模式**：在低性能设备上启用

## 🎨 自定义雪碧图

### 创建自定义雪碧图
1. 使用 `tools/sprite-generator.html` 作为模板
2. 按照规格创建 192x128 像素的雪碧图（6列4行，每帧32x32）
3. 保存为PNG格式，命名为 `{动物名}.png`
4. 放入 `assets/sprites/` 目录

### 雪碧图布局
```
第0行: 静止动画 (4帧)
第1行: 移动动画 (6帧)
第2行: 点击动画 (4帧)
第3行: 悬停动画 (4帧)
```

## 🐛 调试模式

### 启用调试信息
1. 打开浏览器开发者工具 (F12)
2. 在控制台中输入：`localStorage.setItem('petCursorDebug', 'true')`
3. 刷新页面，查看详细的调试信息

### 调试信息说明
- `Pet Cursor 已启动`: 插件成功初始化
- `成功加载雪碧图`: 雪碧图文件加载成功
- `使用占位符模式`: 雪碧图加载失败，使用CSS占位符
- `启用性能模式`: 自动性能优化已激活

## 📞 获取帮助

如果遇到问题：
1. 查看 `test/plugin-test.html` 的调试信息
2. 检查浏览器控制台的错误信息
3. 确认所有文件都已正确放置
4. 尝试重新安装插件

## 🎉 享受使用

安装完成后，你就可以在任何网页上看到可爱的小动物跟随鼠标移动了！

记住：
- 可以随时通过插件图标打开设置面板
- 支持多种动物类型和自定义设置
- 插件会自动适应不同网站和性能环境
- 在特殊页面（如浏览器设置页）会自动禁用
