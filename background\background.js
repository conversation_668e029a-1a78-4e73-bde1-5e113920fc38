// Pet Cursor Background Service Worker
class BackgroundService {
  constructor() {
    this.init();
  }

  init() {
    // 监听插件安装
    chrome.runtime.onInstalled.addListener(this.handleInstalled.bind(this));
    
    // 监听消息
    chrome.runtime.onMessage.addListener(this.handleMessage.bind(this));
    
    // 监听标签页更新
    chrome.tabs.onUpdated.addListener(this.handleTabUpdated.bind(this));
    
    // 监听存储变化
    chrome.storage.onChanged.addListener(this.handleStorageChanged.bind(this));
  }

  async handleInstalled(details) {
    if (details.reason === 'install') {
      // 首次安装时设置默认配置
      await this.setDefaultSettings();
      
      // 打开欢迎页面或设置页面
      chrome.tabs.create({
        url: chrome.runtime.getURL('popup/popup.html')
      });
      
      console.log('Pet Cursor 插件已安装!');
    } else if (details.reason === 'update') {
      // 插件更新时的处理
      console.log('Pet Cursor 插件已更新!');
    }
  }

  async setDefaultSettings() {
    const defaultSettings = {
      enabled: true,
      petType: 'cat',
      speed: 1.0,
      opacity: 100,
      size: 32,
      sensitivity: 0.1,
      clickAnimation: true,
      hoverAnimation: true,
      disabledSites: [] // 禁用的网站列表
    };

    try {
      await chrome.storage.sync.set({ petCursorSettings: defaultSettings });
      console.log('默认设置已保存');
    } catch (error) {
      console.error('保存默认设置失败:', error);
    }
  }

  handleMessage(message, sender, sendResponse) {
    switch (message.type) {
      case 'GET_SETTINGS':
        this.getSettings().then(sendResponse);
        return true; // 异步响应

      case 'SAVE_SETTINGS':
        this.saveSettings(message.settings).then(() => {
          sendResponse({ success: true });
        }).catch(error => {
          sendResponse({ success: false, error: error.message });
        });
        return true;

      case 'TOGGLE_EXTENSION':
        this.toggleExtension(message.enabled).then(sendResponse);
        return true;

      case 'CHECK_SITE_DISABLED':
        this.checkSiteDisabled(message.url).then(sendResponse);
        return true;

      case 'ADD_DISABLED_SITE':
        this.addDisabledSite(message.url).then(sendResponse);
        return true;

      case 'REMOVE_DISABLED_SITE':
        this.removeDisabledSite(message.url).then(sendResponse);
        return true;

      default:
        console.log('未知消息类型:', message.type);
    }
  }

  async handleTabUpdated(tabId, changeInfo, tab) {
    // 当标签页完成加载时，检查是否需要注入脚本
    if (changeInfo.status === 'complete' && tab.url) {
      try {
        const settings = await this.getSettings();
        
        // 检查插件是否启用
        if (!settings.enabled) return;
        
        // 检查当前网站是否被禁用
        const isDisabled = await this.checkSiteDisabled(tab.url);
        if (isDisabled) return;
        
        // 向content script发送设置更新
        chrome.tabs.sendMessage(tabId, {
          type: 'SETTINGS_UPDATED',
          settings: settings
        }).catch(() => {
          // 忽略无法发送消息的错误（某些特殊页面）
        });
        
      } catch (error) {
        console.error('处理标签页更新失败:', error);
      }
    }
  }

  handleStorageChanged(changes, namespace) {
    if (namespace === 'sync' && changes.petCursorSettings) {
      // 设置发生变化时，通知所有标签页
      this.broadcastSettingsUpdate(changes.petCursorSettings.newValue);
    }
  }

  async getSettings() {
    try {
      const result = await chrome.storage.sync.get('petCursorSettings');
      return result.petCursorSettings || await this.getDefaultSettings();
    } catch (error) {
      console.error('获取设置失败:', error);
      return await this.getDefaultSettings();
    }
  }

  async getDefaultSettings() {
    return {
      enabled: true,
      petType: 'cat',
      speed: 1.0,
      opacity: 100,
      size: 32,
      sensitivity: 0.1,
      clickAnimation: true,
      hoverAnimation: true,
      disabledSites: []
    };
  }

  async saveSettings(settings) {
    try {
      await chrome.storage.sync.set({ petCursorSettings: settings });
      
      // 广播设置更新到所有标签页
      this.broadcastSettingsUpdate(settings);
      
      return { success: true };
    } catch (error) {
      console.error('保存设置失败:', error);
      throw error;
    }
  }

  async toggleExtension(enabled) {
    try {
      const settings = await this.getSettings();
      settings.enabled = enabled;
      await this.saveSettings(settings);
      
      return { success: true, enabled: enabled };
    } catch (error) {
      console.error('切换插件状态失败:', error);
      return { success: false, error: error.message };
    }
  }

  async checkSiteDisabled(url) {
    try {
      const settings = await this.getSettings();
      const hostname = new URL(url).hostname;
      
      return settings.disabledSites.includes(hostname);
    } catch (error) {
      console.error('检查网站禁用状态失败:', error);
      return false;
    }
  }

  async addDisabledSite(url) {
    try {
      const settings = await this.getSettings();
      const hostname = new URL(url).hostname;
      
      if (!settings.disabledSites.includes(hostname)) {
        settings.disabledSites.push(hostname);
        await this.saveSettings(settings);
      }
      
      return { success: true };
    } catch (error) {
      console.error('添加禁用网站失败:', error);
      return { success: false, error: error.message };
    }
  }

  async removeDisabledSite(url) {
    try {
      const settings = await this.getSettings();
      const hostname = new URL(url).hostname;
      
      const index = settings.disabledSites.indexOf(hostname);
      if (index > -1) {
        settings.disabledSites.splice(index, 1);
        await this.saveSettings(settings);
      }
      
      return { success: true };
    } catch (error) {
      console.error('移除禁用网站失败:', error);
      return { success: false, error: error.message };
    }
  }

  async broadcastSettingsUpdate(settings) {
    try {
      const tabs = await chrome.tabs.query({});
      
      tabs.forEach(tab => {
        if (tab.id && tab.url && !tab.url.startsWith('chrome://')) {
          chrome.tabs.sendMessage(tab.id, {
            type: 'SETTINGS_UPDATED',
            settings: settings
          }).catch(() => {
            // 忽略无法发送消息的错误
          });
        }
      });
    } catch (error) {
      console.error('广播设置更新失败:', error);
    }
  }

  // 性能监控
  async monitorPerformance() {
    // 可以在这里添加性能监控逻辑
    // 比如监控内存使用、CPU占用等
  }
}

// 初始化背景服务
const backgroundService = new BackgroundService();

// 保持service worker活跃
chrome.runtime.onStartup.addListener(() => {
  console.log('Pet Cursor service worker started');
});

// 处理插件图标点击
chrome.action.onClicked.addListener(async (tab) => {
  // 这里可以添加图标点击的处理逻辑
  // 由于我们使用popup，这个事件通常不会触发
  console.log('Extension icon clicked');
});
