<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Pet Cursor 设置</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="popup-container">
    <!-- 头部 -->
    <div class="header">
      <div class="logo">
        <img src="../assets/icons/icon32.png" alt="Pet Cursor" class="logo-icon">
        <h1>Pet Cursor</h1>
      </div>
      <div class="toggle-container">
        <label class="toggle-switch">
          <input type="checkbox" id="enableToggle" checked>
          <span class="toggle-slider"></span>
        </label>
      </div>
    </div>

    <!-- 动物选择 -->
    <div class="section">
      <h3>选择你的宠物</h3>
      <div class="pet-selection">
        <div class="pet-option active" data-pet="cat">
          <div class="pet-preview cat"></div>
          <span>小猫咪</span>
        </div>
        <div class="pet-option" data-pet="dog">
          <div class="pet-preview dog"></div>
          <span>小狗狗</span>
        </div>
        <div class="pet-option" data-pet="rabbit">
          <div class="pet-preview rabbit"></div>
          <span>小兔子</span>
        </div>
        <div class="pet-option" data-pet="bird">
          <div class="pet-preview bird"></div>
          <span>小鸟</span>
        </div>
        <div class="pet-option" data-pet="panda">
          <div class="pet-preview panda"></div>
          <span>小熊猫</span>
        </div>
      </div>
    </div>

    <!-- 设置选项 -->
    <div class="section">
      <h3>动画设置</h3>
      
      <!-- 动画速度 -->
      <div class="setting-item">
        <label for="speedSlider">动画速度</label>
        <div class="slider-container">
          <input type="range" id="speedSlider" min="0.5" max="2" step="0.1" value="1">
          <span class="slider-value">1.0x</span>
        </div>
      </div>

      <!-- 透明度 -->
      <div class="setting-item">
        <label for="opacitySlider">透明度</label>
        <div class="slider-container">
          <input type="range" id="opacitySlider" min="30" max="100" step="5" value="100">
          <span class="slider-value">100%</span>
        </div>
      </div>

      <!-- 大小 -->
      <div class="setting-item">
        <label for="sizeSlider">大小</label>
        <div class="slider-container">
          <input type="range" id="sizeSlider" min="24" max="64" step="8" value="32">
          <span class="slider-value">32px</span>
        </div>
      </div>
    </div>

    <!-- 高级设置 -->
    <div class="section">
      <h3>高级设置</h3>
      
      <div class="setting-item">
        <label for="sensitivitySlider">跟随灵敏度</label>
        <div class="slider-container">
          <input type="range" id="sensitivitySlider" min="0.05" max="0.3" step="0.05" value="0.1">
          <span class="slider-value">0.1</span>
        </div>
      </div>

      <div class="setting-item checkbox-item">
        <label>
          <input type="checkbox" id="clickAnimationToggle" checked>
          <span class="checkmark"></span>
          点击动画
        </label>
      </div>

      <div class="setting-item checkbox-item">
        <label>
          <input type="checkbox" id="hoverAnimationToggle" checked>
          <span class="checkmark"></span>
          悬停动画
        </label>
      </div>

      <div class="setting-item checkbox-item">
        <label>
          <input type="checkbox" id="performanceModeToggle">
          <span class="checkmark"></span>
          性能模式
        </label>
      </div>
    </div>

    <!-- 网站管理 -->
    <div class="section">
      <h3>网站管理</h3>

      <div class="setting-item">
        <label>当前网站</label>
        <div class="current-site">
          <span id="currentSiteUrl">加载中...</span>
          <button id="toggleCurrentSite" class="btn btn-small">禁用</button>
        </div>
      </div>

      <div class="setting-item">
        <label>禁用网站列表</label>
        <div class="disabled-sites-list" id="disabledSitesList">
          <!-- 动态生成禁用网站列表 -->
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="footer">
      <button id="resetButton" class="btn btn-secondary">重置设置</button>
      <button id="saveButton" class="btn btn-primary">保存设置</button>
    </div>

    <!-- 预览区域 -->
    <div class="preview-area" id="previewArea">
      <div class="preview-pet" id="previewPet"></div>
      <span class="preview-text">移动鼠标预览效果</span>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
