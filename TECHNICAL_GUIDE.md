# Pet Cursor 技术实现指南

## 🏗️ 技术架构概览

### 核心组件
```
Pet Cursor Extension
├── Manifest V3 配置
├── Content Script (注入到网页)
├── Background Service Worker
├── Popup 设置界面
└── 雪碧图动画引擎
```

## 🎯 关键技术实现

### 1. 鼠标跟随算法

#### 基础跟随实现
```javascript
class MouseFollower {
  constructor() {
    this.element = null;
    this.mouseX = 0;
    this.mouseY = 0;
    this.currentX = 0;
    this.currentY = 0;
    this.isAnimating = false;
  }

  init() {
    // 创建跟随元素
    this.createElement();
    
    // 绑定鼠标事件
    document.addEventListener('mousemove', this.handleMouseMove.bind(this));
    
    // 启动动画循环
    this.startAnimation();
  }

  handleMouseMove(e) {
    this.mouseX = e.clientX;
    this.mouseY = e.clientY;
  }

  startAnimation() {
    if (this.isAnimating) return;
    this.isAnimating = true;
    this.animate();
  }

  animate() {
    // 缓动算法 - 让跟随更自然
    const easing = 0.1;
    const deltaX = this.mouseX - this.currentX;
    const deltaY = this.mouseY - this.currentY;
    
    this.currentX += deltaX * easing;
    this.currentY += deltaY * easing;
    
    // 更新元素位置
    this.updatePosition();
    
    // 继续动画循环
    requestAnimationFrame(() => this.animate());
  }

  updatePosition() {
    if (this.element) {
      this.element.style.transform = 
        `translate(${this.currentX}px, ${this.currentY}px)`;
    }
  }
}
```

### 2. 雪碧图动画系统

#### 动画引擎核心
```javascript
class SpriteAnimator {
  constructor(element, spriteConfig) {
    this.element = element;
    this.config = spriteConfig;
    this.currentFrame = 0;
    this.currentAnimation = 'idle';
    this.isPlaying = false;
    this.frameTimer = null;
  }

  // 雪碧图配置示例
  static getDefaultConfig() {
    return {
      spriteWidth: 32,
      spriteHeight: 32,
      animations: {
        idle: { frames: 4, row: 0, fps: 8 },
        walk: { frames: 6, row: 1, fps: 12 },
        click: { frames: 4, row: 2, fps: 15 },
        hover: { frames: 4, row: 3, fps: 10 }
      }
    };
  }

  play(animationName) {
    if (!this.config.animations[animationName]) return;
    
    this.currentAnimation = animationName;
    this.currentFrame = 0;
    this.isPlaying = true;
    
    this.startFrameLoop();
  }

  startFrameLoop() {
    if (this.frameTimer) clearInterval(this.frameTimer);
    
    const animation = this.config.animations[this.currentAnimation];
    const frameDelay = 1000 / animation.fps;
    
    this.frameTimer = setInterval(() => {
      this.nextFrame();
    }, frameDelay);
  }

  nextFrame() {
    const animation = this.config.animations[this.currentAnimation];
    this.currentFrame = (this.currentFrame + 1) % animation.frames;
    this.updateSprite();
  }

  updateSprite() {
    const animation = this.config.animations[this.currentAnimation];
    const x = this.currentFrame * this.config.spriteWidth;
    const y = animation.row * this.config.spriteHeight;
    
    this.element.style.backgroundPosition = `-${x}px -${y}px`;
  }
}
```

### 3. 状态管理系统

#### 动画状态机
```javascript
class AnimationStateMachine {
  constructor(animator) {
    this.animator = animator;
    this.currentState = 'idle';
    this.stateTimeout = null;
    this.mouseVelocity = { x: 0, y: 0 };
    this.lastMousePos = { x: 0, y: 0 };
  }

  updateMouseState(x, y) {
    // 计算鼠标移动速度
    this.mouseVelocity.x = x - this.lastMousePos.x;
    this.mouseVelocity.y = y - this.lastMousePos.y;
    this.lastMousePos = { x, y };

    // 根据速度决定状态
    const speed = Math.sqrt(
      this.mouseVelocity.x ** 2 + this.mouseVelocity.y ** 2
    );

    if (speed > 5) {
      this.setState('walk');
    } else if (speed < 1) {
      this.setState('idle');
    }
  }

  setState(newState) {
    if (this.currentState === newState) return;
    
    this.currentState = newState;
    this.animator.play(newState);
    
    // 清除之前的超时
    if (this.stateTimeout) {
      clearTimeout(this.stateTimeout);
    }
  }

  onMouseClick() {
    this.setState('click');
    
    // 点击动画播放完后回到idle
    this.stateTimeout = setTimeout(() => {
      this.setState('idle');
    }, 500);
  }

  onMouseHover() {
    if (this.currentState === 'idle') {
      this.setState('hover');
    }
  }
}
```

## 🎨 CSS 优化技巧

### 硬件加速
```css
.pet-cursor {
  /* 启用硬件加速 */
  transform: translateZ(0);
  will-change: transform;
  
  /* 确保在最顶层 */
  position: fixed;
  z-index: 999999;
  pointer-events: none;
  
  /* 雪碧图基础样式 */
  width: 32px;
  height: 32px;
  background-image: url('sprites/cat.png');
  background-repeat: no-repeat;
  
  /* 平滑过渡 */
  transition: opacity 0.3s ease;
}

/* 不同尺寸支持 */
.pet-cursor.size-small { width: 24px; height: 24px; }
.pet-cursor.size-medium { width: 32px; height: 32px; }
.pet-cursor.size-large { width: 48px; height: 48px; }
```

## 🔧 性能优化策略

### 1. 事件节流
```javascript
class PerformanceOptimizer {
  static throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    }
  }

  static debounce(func, delay) {
    let timeoutId;
    return function() {
      const args = arguments;
      const context = this;
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(context, args), delay);
    }
  }
}

// 使用示例
const optimizedMouseMove = PerformanceOptimizer.throttle(
  handleMouseMove, 16 // 约60fps
);
```

### 2. 内存管理
```javascript
class MemoryManager {
  constructor() {
    this.observers = [];
    this.timers = [];
    this.elements = [];
  }

  addObserver(observer) {
    this.observers.push(observer);
  }

  addTimer(timer) {
    this.timers.push(timer);
  }

  addElement(element) {
    this.elements.push(element);
  }

  cleanup() {
    // 清理观察者
    this.observers.forEach(observer => observer.disconnect());
    
    // 清理定时器
    this.timers.forEach(timer => clearInterval(timer));
    
    // 清理DOM元素
    this.elements.forEach(element => {
      if (element.parentNode) {
        element.parentNode.removeChild(element);
      }
    });
    
    // 重置数组
    this.observers = [];
    this.timers = [];
    this.elements = [];
  }
}
```

## 🌐 兼容性处理

### 1. CSS冲突避免
```css
/* 使用唯一的类名前缀 */
.pet-cursor-container {
  /* 重置所有可能的样式冲突 */
  all: initial;
  
  /* 设置必要的样式 */
  position: fixed !important;
  z-index: 2147483647 !important; /* 最大z-index值 */
  pointer-events: none !important;
  user-select: none !important;
  
  /* 防止被其他样式影响 */
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}
```

### 2. 浏览器API兼容
```javascript
class BrowserCompatibility {
  static checkSupport() {
    return {
      requestAnimationFrame: !!window.requestAnimationFrame,
      transform: this.checkCSSSupport('transform'),
      willChange: this.checkCSSSupport('will-change'),
      storage: !!chrome.storage
    };
  }

  static checkCSSSupport(property) {
    const testElement = document.createElement('div');
    return property in testElement.style;
  }

  static getAnimationMethod() {
    return window.requestAnimationFrame ||
           window.webkitRequestAnimationFrame ||
           window.mozRequestAnimationFrame ||
           function(callback) { setTimeout(callback, 16); };
  }
}
```

## 📱 响应式设计

### 移动端适配
```javascript
class ResponsiveHandler {
  constructor() {
    this.isMobile = this.detectMobile();
    this.touchSupport = 'ontouchstart' in window;
  }

  detectMobile() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i
      .test(navigator.userAgent);
  }

  adaptForDevice() {
    if (this.isMobile) {
      // 移动端使用触摸事件
      this.bindTouchEvents();
    } else {
      // 桌面端使用鼠标事件
      this.bindMouseEvents();
    }
  }

  bindTouchEvents() {
    document.addEventListener('touchmove', (e) => {
      const touch = e.touches[0];
      this.updatePosition(touch.clientX, touch.clientY);
    });
  }
}
```

## 🔒 安全考虑

### Content Security Policy
```javascript
// 避免使用eval和内联脚本
class SecureImplementation {
  static loadSprite(spritePath) {
    // 使用安全的资源加载方式
    const img = new Image();
    img.src = chrome.runtime.getURL(spritePath);
    return img;
  }

  static sanitizeUserInput(input) {
    // 清理用户输入
    return input.replace(/[<>\"']/g, '');
  }
}
```

这个技术指南涵盖了实现可爱动物鼠标跟随插件的核心技术要点。每个部分都包含了具体的代码示例和最佳实践，可以作为开发过程中的参考文档。
